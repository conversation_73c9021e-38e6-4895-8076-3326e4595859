<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم تأكيد الطلب - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        .success-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem 0;
        }

        .success-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .success-icon {
            font-size: 5rem;
            color: #28a745;
            margin-bottom: 1.5rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .success-title {
            font-size: 2.5rem;
            color: #28a745;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .success-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .order-details {
            background: #FFE4E1;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: right;
        }

        .order-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #D8BFD8;
        }

        .info-label {
            font-weight: bold;
            color: #8B4B8B;
        }

        .info-value {
            color: #666;
        }

        .order-items {
            margin-top: 1.5rem;
        }

        .order-items h4 {
            color: #8B4B8B;
            margin-bottom: 1rem;
            text-align: center;
        }

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid #D8BFD8;
        }

        .item:last-child {
            border-bottom: none;
        }

        .item-name {
            font-weight: 500;
            color: #8B4B8B;
        }

        .item-quantity {
            color: #666;
            font-size: 0.9rem;
        }

        .item-price {
            font-weight: bold;
            color: #FF69B4;
        }

        .total-amount {
            background: #8B4B8B;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
            text-align: center;
        }

        .next-steps {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-right: 4px solid #28a745;
        }

        .next-steps h4 {
            color: #28a745;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .steps-list {
            list-style: none;
            color: #666;
        }

        .steps-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .steps-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            width: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 180px;
        }

        .btn-primary {
            background: #FF69B4;
            color: white;
        }

        .btn-primary:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #8B4B8B;
            border: 2px solid #8B4B8B;
        }

        .btn-secondary:hover {
            background: #8B4B8B;
            color: white;
        }

        .contact-info {
            background: #fff3cd;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            text-align: center;
            border: 1px solid #ffeaa7;
        }

        .contact-info h5 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .contact-info p {
            color: #856404;
            font-size: 0.9rem;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .success-card {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .success-title {
                font-size: 2rem;
            }

            .success-icon {
                font-size: 4rem;
            }

            .order-info {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
            </div>
        </div>
    </header>

    <section class="success-content">
        <div class="container">
            <div class="success-card">
                <div class="success-icon">✅</div>
                <h1 class="success-title">تم تأكيد طلبك بنجاح!</h1>
                <p class="success-message">
                    شكراً لك على ثقتك في فيلا الحلويات. تم استلام طلبك وسيتم تحضيره بعناية فائقة.
                    ستصلك رسالة تأكيد على رقم هاتفك قريباً.
                </p>

                <div class="order-details">
                    <div class="order-info">
                        <div class="info-item">
                            <span class="info-label">رقم الطلب:</span>
                            <span class="info-value">#VS2024001</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الطلب:</span>
                            <span class="info-value">15 ديسمبر 2024</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">طريقة الدفع:</span>
                            <span class="info-value">الدفع عند الاستلام</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">وقت التوصيل المتوقع:</span>
                            <span class="info-value">1-2 يوم عمل</span>
                        </div>
                    </div>

                    <div class="order-items">
                        <h4>تفاصيل الطلب</h4>
                        <div class="item">
                            <div>
                                <div class="item-name">كب كيك الفانيليا</div>
                                <div class="item-quantity">الكمية: 2</div>
                            </div>
                            <div class="item-price">30,000 د.ع</div>
                        </div>
                        <div class="item">
                            <div>
                                <div class="item-name">تورتة الشوكولاتة</div>
                                <div class="item-quantity">الكمية: 1</div>
                            </div>
                            <div class="item-price">47,000 د.ع</div>
                        </div>
                        <div class="item">
                            <div>
                                <div class="item-name">كوكيز اللوز</div>
                                <div class="item-quantity">الكمية: 1</div>
                            </div>
                            <div class="item-price">25,000 د.ع</div>
                        </div>
                    </div>

                    <div class="total-amount">
                        المجموع الكلي: 107,000 د.ع
                    </div>
                </div>

                <div class="next-steps">
                    <h4>📋 الخطوات التالية:</h4>
                    <ul class="steps-list">
                        <li>سنتصل بك خلال ساعة لتأكيد الطلب</li>
                        <li>سيتم تحضير طلبك بعناية فائقة</li>
                        <li>سنرسل لك رسالة عند خروج الطلب للتوصيل</li>
                        <li>سيصل طلبك خلال 1-2 يوم عمل</li>
                    </ul>
                </div>

                <div class="action-buttons">
                    <a href="products.html" class="btn btn-primary">متابعة التسوق</a>
                    <a href="account.html" class="btn btn-secondary">تتبع الطلب</a>
                </div>

                <div class="contact-info">
                    <h5>هل تحتاج مساعدة؟</h5>
                    <p>اتصل بنا على: 07XX XXX XXXX أو راسلنا على: <EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        // إضافة تأثير الاحتفال
        function createConfetti() {
            const colors = ['#FF69B4', '#FFB6C1', '#D8BFD8', '#FFE4E1'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'fixed';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.top = '-10px';
                    confetti.style.width = '10px';
                    confetti.style.height = '10px';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.pointerEvents = 'none';
                    confetti.style.zIndex = '9999';
                    confetti.style.animation = 'fall 3s linear forwards';
                    
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 100);
            }
        }

        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                }
            }
        `;
        document.head.appendChild(style);

        // تشغيل الاحتفال عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(createConfetti, 500);
        });
    </script>
</body>
</html>
