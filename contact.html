<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-header {
            background: white;
            padding: 3rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: #666;
        }

        .contact-content {
            padding: 4rem 0;
        }

        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .contact-info {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .contact-form {
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.8rem;
            color: #8B4B8B;
            margin-bottom: 2rem;
            text-align: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #FFE4E1;
            border-radius: 10px;
            transition: transform 0.3s;
        }

        .contact-item:hover {
            transform: translateY(-2px);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .contact-details h3 {
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .contact-details p {
            color: #666;
            margin-bottom: 0.3rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #8B4B8B;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #FF69B4;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .btn {
            background: #FF69B4;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .working-hours {
            background: #FFE4E1;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .working-hours h4 {
            color: #8B4B8B;
            margin-bottom: 1rem;
            text-align: center;
        }

        .hours-list {
            list-style: none;
        }

        .hours-list li {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #D8BFD8;
        }

        .hours-list li:last-child {
            border-bottom: none;
        }

        .day {
            font-weight: 500;
            color: #8B4B8B;
        }

        .time {
            color: #666;
        }

        .map-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .map-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            margin-top: 1rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .social-link {
            width: 50px;
            height: 50px;
            background: #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            transition: all 0.3s;
        }

        .social-link:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .contact-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .contact-item {
                flex-direction: column;
                text-align: center;
            }

            .page-title {
                font-size: 2rem;
            }

            .contact-info,
            .contact-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html" style="color: #FF69B4;">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">اتصل بنا</h1>
            <p class="page-subtitle">نحن هنا لخدمتك والإجابة على جميع استفساراتك</p>
        </div>
    </section>

    <section class="contact-content">
        <div class="container">
            <div class="contact-container">
                <div class="contact-info">
                    <h2 class="section-title">معلومات التواصل</h2>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-details">
                            <h3>الهاتف</h3>
                            <p>07XX XXX XXXX</p>
                            <p>متاح 24/7 لخدمتك</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h3>البريد الإلكتروني</h3>
                            <p><EMAIL></p>
                            <p>سنرد خلال 24 ساعة</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h3>العنوان</h3>
                            <p>شارع الكرادة، بغداد</p>
                            <p>العراق</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">🚚</div>
                        <div class="contact-details">
                            <h3>التوصيل</h3>
                            <p>جميع أنحاء العراق</p>
                            <p>توصيل سريع وآمن</p>
                        </div>
                    </div>

                    <div class="working-hours">
                        <h4>ساعات العمل</h4>
                        <ul class="hours-list">
                            <li>
                                <span class="day">السبت - الخميس</span>
                                <span class="time">9:00 ص - 10:00 م</span>
                            </li>
                            <li>
                                <span class="day">الجمعة</span>
                                <span class="time">2:00 م - 10:00 م</span>
                            </li>
                        </ul>
                    </div>

                    <div class="social-links">
                        <a href="#" class="social-link">📘</a>
                        <a href="#" class="social-link">📷</a>
                        <a href="#" class="social-link">🐦</a>
                        <a href="#" class="social-link">📱</a>
                    </div>
                </div>

                <div class="contact-form">
                    <h2 class="section-title">أرسل لنا رسالة</h2>
                    
                    <form id="contactForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">الاسم الأول *</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">الاسم الأخير *</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" placeholder="07XXXXXXXXX" required>
                        </div>

                        <div class="form-group">
                            <label for="subject">الموضوع *</label>
                            <select id="subject" name="subject" required>
                                <option value="">اختر الموضوع</option>
                                <option value="order">استفسار عن طلب</option>
                                <option value="product">استفسار عن منتج</option>
                                <option value="complaint">شكوى</option>
                                <option value="suggestion">اقتراح</option>
                                <option value="partnership">شراكة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">الرسالة *</label>
                            <textarea id="message" name="message" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>

                        <button type="submit" class="btn">إرسال الرسالة</button>
                    </form>
                </div>
            </div>

            <div class="map-section">
                <h2 class="section-title">موقعنا على الخريطة</h2>
                <div class="map-placeholder">🗺️</div>
                <p style="margin-top: 1rem; color: #666;">شارع الكرادة، بغداد، العراق</p>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // التحقق من صحة البيانات
            const phone = document.getElementById('phone').value;
            if (!phone.match(/^07\d{9}$/)) {
                alert('يرجى إدخال رقم هاتف عراقي صحيح (07XXXXXXXXX)');
                return;
            }

            // محاكاة إرسال الرسالة
            alert('شكراً لك! تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.');
            this.reset();
        });
    </script>
</body>
</html>
