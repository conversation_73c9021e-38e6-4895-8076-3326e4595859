<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>من نحن - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-hero {
            background: linear-gradient(rgba(255,182,193,0.8), rgba(255,182,193,0.8)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 400"><rect fill="%23FFE4E1" width="1200" height="400"/><circle fill="%23D8BFD8" cx="200" cy="100" r="40" opacity="0.3"/><circle fill="%23FF69B4" cx="800" cy="200" r="60" opacity="0.2"/></svg>');
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .page-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-hero p {
            font-size: 1.2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .about-content {
            padding: 4rem 0;
        }

        .about-section {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 2rem;
            color: #8B4B8B;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: #FF69B4;
            border-radius: 2px;
        }

        .story-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            margin-bottom: 3rem;
        }

        .story-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #666;
        }

        .story-image {
            height: 300px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6rem;
            color: white;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .value-card {
            background: #FFE4E1;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s;
        }

        .value-card:hover {
            transform: translateY(-5px);
        }

        .value-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .value-title {
            font-size: 1.3rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .value-description {
            color: #666;
            line-height: 1.6;
        }

        .team-section {
            background: #FFE4E1;
            padding: 4rem 0;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .team-member {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .team-member:hover {
            transform: translateY(-5px);
        }

        .member-photo {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
        }

        .member-name {
            font-size: 1.3rem;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .member-role {
            color: #FF69B4;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .member-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .stats-section {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            margin: 3rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            padding: 1rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #FF69B4;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #8B4B8B;
            font-weight: 500;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .page-hero h1 {
                font-size: 2rem;
            }

            .story-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .story-image {
                height: 200px;
                font-size: 4rem;
            }

            .about-section {
                padding: 2rem 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html" style="color: #FF69B4;">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-hero">
        <div class="container">
            <h1>من نحن</h1>
            <p>قصة شغف بصناعة أجمل اللحظات الحلوة</p>
        </div>
    </section>

    <section class="about-content">
        <div class="container">
            <div class="about-section">
                <h2 class="section-title">قصتنا</h2>
                <div class="story-content">
                    <div class="story-text">
                        <p>بدأت قصة فيلا الحلويات في عام 2020 من حلم بسيط: صناعة أجود أنواع الحلويات التي تجمع العائلات وتحلي اللحظات الخاصة. انطلقنا من مطبخ صغير بشغف كبير وحب للتفاصيل.</p>
                        
                        <p>اليوم، نفخر بأننا أصبحنا واحداً من أهم متاجر الحلويات في العراق، نخدم آلاف العملاء الذين يثقون بجودة منتجاتنا وخدماتنا المميزة.</p>
                        
                        <p>نؤمن بأن كل قطعة حلوى تحمل في طياتها قصة وذكرى جميلة، لذلك نحرص على صناعة منتجاتنا بأعلى معايير الجودة والطعم الأصيل.</p>
                    </div>
                    <div class="story-image">🏪</div>
                </div>
            </div>

            <div class="about-section">
                <h2 class="section-title">قيمنا</h2>
                <div class="values-grid">
                    <div class="value-card">
                        <div class="value-icon">🏆</div>
                        <h3 class="value-title">الجودة</h3>
                        <p class="value-description">نستخدم أجود المكونات الطبيعية ونتبع أعلى معايير الجودة في كل منتج نصنعه</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">❤️</div>
                        <h3 class="value-title">الشغف</h3>
                        <p class="value-description">نصنع كل قطعة حلوى بحب وشغف، لأننا نؤمن بأن الطعم الحقيقي يأتي من القلب</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🤝</div>
                        <h3 class="value-title">الثقة</h3>
                        <p class="value-description">نبني علاقات طويلة الأمد مع عملائنا من خلال الصدق والشفافية في كل تعاملاتنا</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🌟</div>
                        <h3 class="value-title">الإبداع</h3>
                        <p class="value-description">نطور منتجاتنا باستمرار ونبتكر نكهات جديدة لنلبي تطلعات عملائنا</p>
                    </div>
                </div>
            </div>

            <div class="stats-section">
                <h2 class="section-title">إنجازاتنا بالأرقام</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">5000+</div>
                        <div class="stat-label">عميل سعيد</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">نوع من الحلويات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4</div>
                        <div class="stat-label">سنوات من الخبرة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">رضا العملاء</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="team-section">
        <div class="container">
            <h2 class="section-title">فريق العمل</h2>
            <div class="team-grid">
                <div class="team-member">
                    <div class="member-photo">👨‍🍳</div>
                    <h3 class="member-name">أحمد محمد</h3>
                    <p class="member-role">الشيف الرئيسي</p>
                    <p class="member-description">خبرة 15 عاماً في صناعة الحلويات الشرقية والغربية، متخصص في الكيك والتورتات</p>
                </div>
                <div class="team-member">
                    <div class="member-photo">👩‍💼</div>
                    <h3 class="member-name">فاطمة علي</h3>
                    <p class="member-role">مديرة الجودة</p>
                    <p class="member-description">تضمن جودة جميع المنتجات وتطبيق معايير السلامة الغذائية</p>
                </div>
                <div class="team-member">
                    <div class="member-photo">👨‍💻</div>
                    <h3 class="member-name">محمد حسن</h3>
                    <p class="member-role">مدير خدمة العملاء</p>
                    <p class="member-description">يهتم بتجربة العملاء ويضمن حصولهم على أفضل خدمة ممكنة</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
