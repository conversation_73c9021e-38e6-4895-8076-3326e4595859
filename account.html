<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حسابي - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-header {
            background: white;
            padding: 2rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .account-content {
            padding: 3rem 0;
        }

        .account-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 3rem;
        }

        .account-sidebar {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 2rem;
        }

        .user-info {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }

        .user-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .user-email {
            color: #666;
            font-size: 0.9rem;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            color: #8B4B8B;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #FFE4E1;
            color: #FF69B4;
        }

        .account-main {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.8rem;
            color: #8B4B8B;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .orders-list {
            margin-top: 1rem;
        }

        .order-item {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: box-shadow 0.3s;
        }

        .order-item:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .order-number {
            font-weight: bold;
            color: #8B4B8B;
        }

        .order-status {
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-shipped {
            background: #d4edda;
            color: #155724;
        }

        .status-delivered {
            background: #d1ecf1;
            color: #0c5460;
        }

        .order-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            font-weight: 500;
            color: #8B4B8B;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #8B4B8B;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #FF69B4;
        }

        .btn {
            background: #FF69B4;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-outline:hover {
            background: #FF69B4;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #FFE4E1;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FF69B4;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #8B4B8B;
            font-weight: 500;
        }

        .address-list {
            margin-top: 1rem;
        }

        .address-item {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .address-default {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #28a745;
            color: white;
            padding: 0.2rem 0.8rem;
            border-radius: 10px;
            font-size: 0.8rem;
        }

        .address-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .account-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .account-sidebar {
                position: static;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .order-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html" style="color: #FF69B4;">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">حسابي</h1>
            <p>إدارة حسابك وطلباتك</p>
        </div>
    </section>

    <section class="account-content">
        <div class="container">
            <div class="account-container">
                <div class="account-sidebar">
                    <div class="user-info">
                        <div class="user-avatar">👤</div>
                        <div class="user-name">أحمد محمد</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                    
                    <ul class="sidebar-menu">
                        <li><a href="#" class="active" onclick="showTab('dashboard')">📊 لوحة التحكم</a></li>
                        <li><a href="#" onclick="showTab('orders')">📦 طلباتي</a></li>
                        <li><a href="#" onclick="showTab('profile')">👤 الملف الشخصي</a></li>
                        <li><a href="#" onclick="showTab('addresses')">📍 العناوين</a></li>
                        <li><a href="#" onclick="showTab('favorites')">❤️ المفضلة</a></li>
                        <li><a href="#" onclick="showTab('settings')">⚙️ الإعدادات</a></li>
                        <li><a href="index.html">🚪 تسجيل الخروج</a></li>
                    </ul>
                </div>

                <div class="account-main">
                    <div id="dashboard" class="tab-content active">
                        <h2 class="section-title">
                            <span>📊</span>
                            لوحة التحكم
                        </h2>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">12</div>
                                <div class="stat-label">إجمالي الطلبات</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3</div>
                                <div class="stat-label">طلبات قيد التنفيذ</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">450,000</div>
                                <div class="stat-label">إجمالي المشتريات (د.ع)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8</div>
                                <div class="stat-label">المنتجات المفضلة</div>
                            </div>
                        </div>

                        <h3 style="color: #8B4B8B; margin-bottom: 1rem;">آخر الطلبات</h3>
                        <div class="orders-list">
                            <div class="order-item">
                                <div class="order-header">
                                    <span class="order-number">طلب #VS2024001</span>
                                    <span class="order-status status-shipped">قيد الشحن</span>
                                </div>
                                <div class="order-details">
                                    <div class="detail-item">
                                        <span class="detail-label">التاريخ:</span>
                                        <span class="detail-value">15 ديسمبر 2024</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">المبلغ:</span>
                                        <span class="detail-value">107,000 د.ع</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="orders" class="tab-content">
                        <h2 class="section-title">
                            <span>📦</span>
                            طلباتي
                        </h2>
                        
                        <div class="orders-list">
                            <div class="order-item">
                                <div class="order-header">
                                    <span class="order-number">طلب #VS2024001</span>
                                    <span class="order-status status-shipped">قيد الشحن</span>
                                </div>
                                <div class="order-details">
                                    <div class="detail-item">
                                        <span class="detail-label">التاريخ:</span>
                                        <span class="detail-value">15 ديسمبر 2024</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">المبلغ:</span>
                                        <span class="detail-value">107,000 د.ع</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">العناصر:</span>
                                        <span class="detail-value">3 منتجات</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">التوصيل المتوقع:</span>
                                        <span class="detail-value">17 ديسمبر 2024</span>
                                    </div>
                                </div>
                                <button class="btn btn-outline">تتبع الطلب</button>
                            </div>

                            <div class="order-item">
                                <div class="order-header">
                                    <span class="order-number">طلب #VS2024002</span>
                                    <span class="order-status status-delivered">تم التسليم</span>
                                </div>
                                <div class="order-details">
                                    <div class="detail-item">
                                        <span class="detail-label">التاريخ:</span>
                                        <span class="detail-value">10 ديسمبر 2024</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">المبلغ:</span>
                                        <span class="detail-value">85,000 د.ع</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">العناصر:</span>
                                        <span class="detail-value">2 منتجات</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">تاريخ التسليم:</span>
                                        <span class="detail-value">12 ديسمبر 2024</span>
                                    </div>
                                </div>
                                <button class="btn btn-outline">إعادة الطلب</button>
                            </div>
                        </div>
                    </div>

                    <div id="profile" class="tab-content">
                        <h2 class="section-title">
                            <span>👤</span>
                            الملف الشخصي
                        </h2>
                        
                        <form>
                            <div class="form-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="firstName">الاسم الأول</label>
                                        <input type="text" id="firstName" value="أحمد">
                                    </div>
                                    <div class="form-group">
                                        <label for="lastName">الاسم الأخير</label>
                                        <input type="text" id="lastName" value="محمد">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">البريد الإلكتروني</label>
                                    <input type="email" id="email" value="<EMAIL>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone">رقم الهاتف</label>
                                    <input type="tel" id="phone" value="07XX XXX XXXX">
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="birthDate">تاريخ الميلاد</label>
                                        <input type="date" id="birthDate">
                                    </div>
                                    <div class="form-group">
                                        <label for="gender">الجنس</label>
                                        <select id="gender">
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn">حفظ التغييرات</button>
                        </form>
                    </div>

                    <div id="addresses" class="tab-content">
                        <h2 class="section-title">
                            <span>📍</span>
                            العناوين المحفوظة
                        </h2>
                        
                        <div class="address-list">
                            <div class="address-item">
                                <div class="address-default">العنوان الافتراضي</div>
                                <h4 style="color: #8B4B8B; margin-bottom: 0.5rem;">المنزل</h4>
                                <p>شارع الكرادة، بناية 15، الطابق الثالث</p>
                                <p>بغداد، العراق</p>
                                <p>📞 07XX XXX XXXX</p>
                                <div class="address-actions">
                                    <button class="btn btn-outline">تعديل</button>
                                    <button class="btn btn-outline">حذف</button>
                                </div>
                            </div>
                            
                            <div class="address-item">
                                <h4 style="color: #8B4B8B; margin-bottom: 0.5rem;">العمل</h4>
                                <p>شارع المتنبي، مجمع الأعمال</p>
                                <p>بغداد، العراق</p>
                                <p>📞 07XX XXX XXXX</p>
                                <div class="address-actions">
                                    <button class="btn btn-outline">تعديل</button>
                                    <button class="btn btn-outline">حذف</button>
                                </div>
                            </div>
                        </div>
                        
                        <button class="btn">إضافة عنوان جديد</button>
                    </div>

                    <div id="favorites" class="tab-content">
                        <h2 class="section-title">
                            <span>❤️</span>
                            المنتجات المفضلة
                        </h2>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <div style="background: #FFE4E1; border-radius: 10px; padding: 1rem; text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🧁</div>
                                <h4 style="color: #8B4B8B; margin-bottom: 0.5rem;">كب كيك الفانيليا</h4>
                                <p style="color: #FF69B4; font-weight: bold; margin-bottom: 1rem;">15,000 د.ع</p>
                                <button class="btn btn-outline">إضافة للسلة</button>
                            </div>
                            
                            <div style="background: #FFE4E1; border-radius: 10px; padding: 1rem; text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🍰</div>
                                <h4 style="color: #8B4B8B; margin-bottom: 0.5rem;">تورتة الشوكولاتة</h4>
                                <p style="color: #FF69B4; font-weight: bold; margin-bottom: 1rem;">45,000 د.ع</p>
                                <button class="btn btn-outline">إضافة للسلة</button>
                            </div>
                        </div>
                    </div>

                    <div id="settings" class="tab-content">
                        <h2 class="section-title">
                            <span>⚙️</span>
                            الإعدادات
                        </h2>
                        
                        <div class="form-section">
                            <h3 style="color: #8B4B8B; margin-bottom: 1rem;">إعدادات الإشعارات</h3>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="checkbox" checked>
                                    إشعارات الطلبات عبر البريد الإلكتروني
                                </label>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="checkbox" checked>
                                    إشعارات العروض والخصومات
                                </label>
                            </div>
                            <div style="margin-bottom: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="checkbox">
                                    إشعارات المنتجات الجديدة
                                </label>
                            </div>
                            
                            <h3 style="color: #8B4B8B; margin-bottom: 1rem;">تغيير كلمة المرور</h3>
                            <div class="form-group">
                                <label for="currentPassword">كلمة المرور الحالية</label>
                                <input type="password" id="currentPassword">
                            </div>
                            <div class="form-group">
                                <label for="newPassword">كلمة المرور الجديدة</label>
                                <input type="password" id="newPassword">
                            </div>
                            <div class="form-group">
                                <label for="confirmPassword">تأكيد كلمة المرور</label>
                                <input type="password" id="confirmPassword">
                            </div>
                            
                            <button class="btn">حفظ الإعدادات</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إزالة التحديد من جميع روابط القائمة
            document.querySelectorAll('.sidebar-menu a').forEach(link => {
                link.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            
            // تحديد الرابط النشط
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
