<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عربة التسوق - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .page-header {
            background: white;
            padding: 2rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .cart-content {
            padding: 3rem 0;
        }

        .cart-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
        }

        .cart-items {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .cart-item {
            display: grid;
            grid-template-columns: 100px 1fr auto auto auto;
            gap: 1rem;
            align-items: center;
            padding: 1.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
        }

        .item-details h3 {
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .item-details p {
            color: #666;
            font-size: 0.9rem;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 2px solid #D8BFD8;
            background: white;
            border-radius: 50%;
            font-weight: bold;
            color: #8B4B8B;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quantity-btn:hover {
            background: #FF69B4;
            color: white;
            border-color: #FF69B4;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            padding: 0.5rem;
            border: 2px solid #D8BFD8;
            border-radius: 5px;
        }

        .item-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FF69B4;
        }

        .remove-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .remove-btn:hover {
            background: #ff3742;
        }

        .cart-summary {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 2rem;
        }

        .summary-title {
            font-size: 1.5rem;
            color: #8B4B8B;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
        }

        .summary-row.total {
            border-top: 2px solid #D8BFD8;
            padding-top: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #8B4B8B;
        }

        .discount-code {
            margin: 1.5rem 0;
        }

        .discount-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            margin-bottom: 0.5rem;
        }

        .btn {
            display: block;
            width: 100%;
            background: #FF69B4;
            color: white;
            padding: 1rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-outline:hover {
            background: #FF69B4;
            color: white;
        }

        .empty-cart {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .empty-cart-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-cart h2 {
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .empty-cart p {
            color: #666;
            margin-bottom: 2rem;
        }

        .shipping-info {
            background: #FFE4E1;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .shipping-info h4 {
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .shipping-info p {
            font-size: 0.9rem;
            color: #666;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .cart-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .cart-item {
                grid-template-columns: 80px 1fr;
                gap: 1rem;
            }

            .quantity-controls,
            .item-price,
            .remove-btn {
                grid-column: 1 / -1;
                justify-self: start;
                margin-top: 0.5rem;
            }

            .quantity-controls {
                justify-self: center;
            }

            .item-price {
                justify-self: center;
                font-size: 1.5rem;
            }

            .remove-btn {
                justify-self: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon" style="background: #FF1493;">🛒 السلة (3)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">عربة التسوق</h1>
            <p>راجع منتجاتك المختارة قبل إتمام الطلب</p>
        </div>
    </section>

    <section class="cart-content">
        <div class="container">
            <div class="cart-container">
                <div class="cart-items">
                    <div class="cart-item">
                        <div class="item-image">🧁</div>
                        <div class="item-details">
                            <h3>كب كيك الفانيليا</h3>
                            <p>حجم متوسط - تغليف عادي</p>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity(1, -1)">-</button>
                            <input type="number" class="quantity-input" value="2" min="1" id="qty1">
                            <button class="quantity-btn" onclick="updateQuantity(1, 1)">+</button>
                        </div>
                        <div class="item-price">30,000 د.ع</div>
                        <button class="remove-btn" onclick="removeItem(1)">حذف</button>
                    </div>

                    <div class="cart-item">
                        <div class="item-image">🍰</div>
                        <div class="item-details">
                            <h3>تورتة الشوكولاتة</h3>
                            <p>حجم كبير - تغليف هدايا</p>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity(2, -1)">-</button>
                            <input type="number" class="quantity-input" value="1" min="1" id="qty2">
                            <button class="quantity-btn" onclick="updateQuantity(2, 1)">+</button>
                        </div>
                        <div class="item-price">47,000 د.ع</div>
                        <button class="remove-btn" onclick="removeItem(2)">حذف</button>
                    </div>

                    <div class="cart-item">
                        <div class="item-image">🍪</div>
                        <div class="item-details">
                            <h3>كوكيز اللوز</h3>
                            <p>حجم كبير - تغليف عادي</p>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity(3, -1)">-</button>
                            <input type="number" class="quantity-input" value="1" min="1" id="qty3">
                            <button class="quantity-btn" onclick="updateQuantity(3, 1)">+</button>
                        </div>
                        <div class="item-price">25,000 د.ع</div>
                        <button class="remove-btn" onclick="removeItem(3)">حذف</button>
                    </div>
                </div>

                <div class="cart-summary">
                    <h3 class="summary-title">ملخص الطلب</h3>
                    
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span>102,000 د.ع</span>
                    </div>
                    
                    <div class="summary-row">
                        <span>الضريبة:</span>
                        <span>0 د.ع</span>
                    </div>
                    
                    <div class="summary-row">
                        <span>الشحن:</span>
                        <span>5,000 د.ع</span>
                    </div>
                    
                    <div class="summary-row total">
                        <span>المجموع الكلي:</span>
                        <span>107,000 د.ع</span>
                    </div>

                    <div class="shipping-info">
                        <h4>معلومات الشحن</h4>
                        <p>الشحن مجاني للطلبات أكثر من 50,000 د.ع داخل بغداد</p>
                        <p>مدة التوصيل: 1-2 يوم عمل</p>
                    </div>

                    <div class="discount-code">
                        <input type="text" class="discount-input" placeholder="كود الخصم">
                        <button class="btn btn-outline">تطبيق الكود</button>
                    </div>

                    <a href="checkout.html" class="btn">إتمام الطلب</a>
                    <a href="products.html" class="btn btn-outline">متابعة التسوق</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function updateQuantity(itemId, change) {
            const qtyInput = document.getElementById('qty' + itemId);
            const currentQty = parseInt(qtyInput.value);
            const newQty = currentQty + change;
            
            if (newQty >= 1) {
                qtyInput.value = newQty;
                updateTotals();
            }
        }

        function removeItem(itemId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                // هنا يمكن إضافة كود حذف المنتج
                alert('تم حذف المنتج من السلة');
                updateTotals();
            }
        }

        function updateTotals() {
            // هنا يمكن إضافة كود حساب المجاميع
            console.log('تحديث المجاميع...');
        }
    </script>
</body>
</html>
