<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع المنتجات - فيلا الحلويات</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-brown: #916045;
            --light-beige: #DEDIBA;
            --warm-gold: #D4A574;
            --cream: #F5F1E8;
            --dark-brown: #6B4423;
            --accent-gold: #B8956A;
            --text-dark: #2C1810;
            --text-light: #8B7355;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, var(--cream) 0%, var(--light-beige) 50%, #F8F5F0 100%);
            color: var(--text-dark);
            line-height: 1.7;
            font-weight: 400;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
            padding: 1.2rem 0;
            box-shadow: 0 8px 32px rgba(145, 96, 69, 0.15);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            font-size: 2.2rem;
            font-weight: 800;
            color: var(--cream);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            color: var(--warm-gold);
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2.5rem;
            flex-wrap: wrap;
            align-items: center;
        }

        nav a {
            text-decoration: none;
            color: var(--cream);
            font-weight: 500;
            font-size: 1.05rem;
            position: relative;
            transition: all 0.3s ease;
            padding: 0.5rem 0;
        }

        nav a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--warm-gold);
            transition: width 0.3s ease;
        }

        nav a:hover {
            color: var(--warm-gold);
        }

        nav a:hover::after {
            width: 100%;
        }

        .cart-icon {
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .cart-icon:hover {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.4);
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(212, 165, 116, 0.2) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(222, 219, 186, 0.15) 0%, transparent 50%);
            pointer-events: none;
        }

        .page-title {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--cream);
            margin-bottom: 1rem;
            text-shadow: 2px 4px 8px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .page-header p {
            font-size: 1.3rem;
            color: var(--cream);
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .filters {
            background: linear-gradient(135deg, white 0%, var(--cream) 100%);
            padding: 3rem 0;
            border-bottom: 1px solid rgba(145, 96, 69, 0.1);
            position: relative;
        }

        .filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23D4A574" opacity="0.05"/></svg>');
            pointer-events: none;
        }

        .filter-controls {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .search-box {
            padding: 1rem 1.5rem;
            border: 2px solid var(--light-beige);
            border-radius: 50px;
            font-size: 1.05rem;
            width: 350px;
            max-width: 100%;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(145, 96, 69, 0.1);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--warm-gold);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.2);
            transform: translateY(-2px);
        }

        .filter-select {
            padding: 1rem 1.5rem;
            border: 2px solid var(--light-beige);
            border-radius: 50px;
            font-size: 1.05rem;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(145, 96, 69, 0.1);
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--warm-gold);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.2);
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.05rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(212, 165, 116, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 30px rgba(212, 165, 116, 0.4);
        }

        .products-section {
            padding: 5rem 0;
            background: linear-gradient(135deg, var(--light-beige) 0%, var(--cream) 100%);
            position: relative;
        }

        .products-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%23D4A574" opacity="0.05"/><circle cx="80" cy="80" r="1.5" fill="%23916045" opacity="0.03"/></svg>');
            pointer-events: none;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 3rem;
            margin-top: 3rem;
            position: relative;
            z-index: 2;
        }

        .product-card {
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(145, 96, 69, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(212, 165, 116, 0.15);
            position: relative;
            group: hover;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(212, 165, 116, 0.05) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(145, 96, 69, 0.2);
        }

        .product-image {
            height: 240px;
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(145, 96, 69, 0.3);
            z-index: 3;
        }

        .product-info {
            padding: 2rem;
            position: relative;
        }

        .product-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-brown);
            margin-bottom: 0.8rem;
            line-height: 1.3;
        }

        .product-description {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            font-size: 1rem;
            line-height: 1.6;
        }

        .product-price {
            font-size: 1.8rem;
            color: var(--warm-gold);
            font-weight: 800;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .product-price::before {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--warm-gold);
            border-radius: 50%;
        }

        .product-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
            min-width: 120px;
        }

        .btn-outline {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-outline:hover {
            background: #FF69B4;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }

        .pagination a {
            padding: 0.8rem 1.2rem;
            background: white;
            color: #8B4B8B;
            text-decoration: none;
            border-radius: 5px;
            border: 1px solid #D8BFD8;
            transition: all 0.3s;
        }

        .pagination a:hover,
        .pagination a.active {
            background: #FF69B4;
            color: white;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                width: 100%;
            }

            .product-actions {
                flex-direction: column;
            }

            .btn-small {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html" style="color: #FF69B4;">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">جميع المنتجات</h1>
            <p>اكتشف مجموعتنا الواسعة من الحلويات الشهية</p>
        </div>
    </section>

    <section class="filters">
        <div class="container">
            <div class="filter-controls">
                <input type="text" class="search-box" placeholder="ابحث عن المنتجات...">
                <select class="filter-select">
                    <option value="">جميع الفئات</option>
                    <option value="cakes">التورتات</option>
                    <option value="cupcakes">الكب كيك</option>
                    <option value="cookies">الكوكيز</option>
                    <option value="pastries">المعجنات الحلوة</option>
                </select>
                <select class="filter-select">
                    <option value="">ترتيب حسب</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                    <option value="name">الاسم</option>
                    <option value="newest">الأحدث</option>
                </select>
                <button class="btn">بحث</button>
            </div>
        </div>
    </section>

    <section class="products-section">
        <div class="container">
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        🧁
                        <span class="product-badge">جديد</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">كب كيك الفانيليا</h3>
                        <p class="product-description">كب كيك طري بنكهة الفانيليا الطبيعية مع كريمة الزبدة</p>
                        <p class="product-price">15,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍰</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة الشوكولاتة</h3>
                        <p class="product-description">تورتة شوكولاتة فاخرة بطبقات متعددة وكريمة غنية</p>
                        <p class="product-price">45,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍪</div>
                    <div class="product-info">
                        <h3 class="product-title">كوكيز اللوز</h3>
                        <p class="product-description">كوكيز مقرمش محشو باللوز المحمص والعسل الطبيعي</p>
                        <p class="product-price">25,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        🥧
                        <span class="product-badge">مميز</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">تارت الفراولة</h3>
                        <p class="product-description">تارت بقاعدة مقرمشة وكريمة الفانيليا مع فراولة طازجة</p>
                        <p class="product-price">35,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍩</div>
                    <div class="product-info">
                        <h3 class="product-title">دونات محشوة</h3>
                        <p class="product-description">دونات طرية محشوة بالكريمة ومغطاة بالشوكولاتة</p>
                        <p class="product-price">12,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🎂</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة عيد الميلاد</h3>
                        <p class="product-description">تورتة مخصصة لأعياد الميلاد بتصاميم متنوعة</p>
                        <p class="product-price">60,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pagination">
                <a href="#" class="active">1</a>
                <a href="#">2</a>
                <a href="#">3</a>
                <a href="#">التالي</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
