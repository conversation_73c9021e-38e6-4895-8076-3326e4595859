<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع المنتجات - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-header {
            background: white;
            padding: 2rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .filters {
            background: white;
            padding: 2rem 0;
            border-bottom: 1px solid #eee;
        }

        .filter-controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
        }

        .search-box {
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 25px;
            font-size: 1rem;
            width: 300px;
            max-width: 100%;
        }

        .filter-select {
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 25px;
            font-size: 1rem;
            background: white;
        }

        .btn {
            display: inline-block;
            background: #FF69B4;
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .products-section {
            padding: 3rem 0;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            height: 200px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
        }

        .product-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #FF69B4;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .product-description {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .product-price {
            font-size: 1.5rem;
            color: #FF69B4;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .product-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
            min-width: 120px;
        }

        .btn-outline {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-outline:hover {
            background: #FF69B4;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }

        .pagination a {
            padding: 0.8rem 1.2rem;
            background: white;
            color: #8B4B8B;
            text-decoration: none;
            border-radius: 5px;
            border: 1px solid #D8BFD8;
            transition: all 0.3s;
        }

        .pagination a:hover,
        .pagination a.active {
            background: #FF69B4;
            color: white;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                width: 100%;
            }

            .product-actions {
                flex-direction: column;
            }

            .btn-small {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html" style="color: #FF69B4;">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">جميع المنتجات</h1>
            <p>اكتشف مجموعتنا الواسعة من الحلويات الشهية</p>
        </div>
    </section>

    <section class="filters">
        <div class="container">
            <div class="filter-controls">
                <input type="text" class="search-box" placeholder="ابحث عن المنتجات...">
                <select class="filter-select">
                    <option value="">جميع الفئات</option>
                    <option value="cakes">التورتات</option>
                    <option value="cupcakes">الكب كيك</option>
                    <option value="cookies">الكوكيز</option>
                    <option value="pastries">المعجنات الحلوة</option>
                </select>
                <select class="filter-select">
                    <option value="">ترتيب حسب</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                    <option value="name">الاسم</option>
                    <option value="newest">الأحدث</option>
                </select>
                <button class="btn">بحث</button>
            </div>
        </div>
    </section>

    <section class="products-section">
        <div class="container">
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        🧁
                        <span class="product-badge">جديد</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">كب كيك الفانيليا</h3>
                        <p class="product-description">كب كيك طري بنكهة الفانيليا الطبيعية مع كريمة الزبدة</p>
                        <p class="product-price">15,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍰</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة الشوكولاتة</h3>
                        <p class="product-description">تورتة شوكولاتة فاخرة بطبقات متعددة وكريمة غنية</p>
                        <p class="product-price">45,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍪</div>
                    <div class="product-info">
                        <h3 class="product-title">كوكيز اللوز</h3>
                        <p class="product-description">كوكيز مقرمش محشو باللوز المحمص والعسل الطبيعي</p>
                        <p class="product-price">25,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        🥧
                        <span class="product-badge">مميز</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">تارت الفراولة</h3>
                        <p class="product-description">تارت بقاعدة مقرمشة وكريمة الفانيليا مع فراولة طازجة</p>
                        <p class="product-price">35,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🍩</div>
                    <div class="product-info">
                        <h3 class="product-title">دونات محشوة</h3>
                        <p class="product-description">دونات طرية محشوة بالكريمة ومغطاة بالشوكولاتة</p>
                        <p class="product-price">12,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">🎂</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة عيد الميلاد</h3>
                        <p class="product-description">تورتة مخصصة لأعياد الميلاد بتصاميم متنوعة</p>
                        <p class="product-price">60,000 د.ع</p>
                        <div class="product-actions">
                            <a href="product-details.html" class="btn btn-small btn-outline">التفاصيل</a>
                            <button class="btn btn-small">أضف للسلة</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pagination">
                <a href="#" class="active">1</a>
                <a href="#">2</a>
                <a href="#">3</a>
                <a href="#">التالي</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
