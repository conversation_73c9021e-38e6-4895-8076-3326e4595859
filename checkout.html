<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إتمام الطلب - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        .checkout-steps {
            background: white;
            padding: 2rem 0;
            border-bottom: 1px solid #eee;
        }

        .steps-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            background: #f5f5f5;
            color: #666;
        }

        .step.active {
            background: #FF69B4;
            color: white;
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .page-header {
            background: white;
            padding: 2rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .checkout-content {
            padding: 3rem 0;
        }

        .checkout-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
        }

        .checkout-form {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.3rem;
            color: #8B4B8B;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #8B4B8B;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #FF69B4;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .payment-option {
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .payment-option:hover {
            border-color: #FF69B4;
        }

        .payment-option.selected {
            border-color: #FF69B4;
            background: #FFE4E1;
        }

        .payment-option input[type="radio"] {
            display: none;
        }

        .payment-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .order-summary {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 2rem;
        }

        .summary-title {
            font-size: 1.5rem;
            color: #8B4B8B;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .item-image {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .item-details h4 {
            color: #8B4B8B;
            font-size: 0.9rem;
        }

        .item-details p {
            color: #666;
            font-size: 0.8rem;
        }

        .item-price {
            font-weight: bold;
            color: #FF69B4;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin: 1rem 0;
            padding: 0.5rem 0;
        }

        .summary-row.total {
            border-top: 2px solid #D8BFD8;
            padding-top: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #8B4B8B;
        }

        .btn {
            display: block;
            width: 100%;
            background: #FF69B4;
            color: white;
            padding: 1rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .security-info {
            background: #FFE4E1;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            text-align: center;
        }

        .security-info p {
            font-size: 0.9rem;
            color: #666;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .checkout-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .payment-methods {
                grid-template-columns: 1fr;
            }

            .steps-container {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
            </div>
        </div>
    </header>

    <section class="checkout-steps">
        <div class="container">
            <div class="steps-container">
                <div class="step completed">
                    <span>✓</span>
                    <span>السلة</span>
                </div>
                <div class="step active">
                    <span>2</span>
                    <span>معلومات الشحن</span>
                </div>
                <div class="step">
                    <span>3</span>
                    <span>الدفع</span>
                </div>
                <div class="step">
                    <span>4</span>
                    <span>التأكيد</span>
                </div>
            </div>
        </div>
    </section>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">إتمام الطلب</h1>
            <p>أدخل معلومات الشحن والدفع لإتمام طلبك</p>
        </div>
    </section>

    <section class="checkout-content">
        <div class="container">
            <div class="checkout-container">
                <div class="checkout-form">
                    <form id="checkoutForm">
                        <div class="form-section">
                            <h3 class="section-title">
                                <span>📍</span>
                                معلومات الشحن
                            </h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName">الاسم الأول *</label>
                                    <input type="text" id="firstName" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label for="lastName">الاسم الأخير *</label>
                                    <input type="text" id="lastName" name="lastName" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="phone">رقم الهاتف *</label>
                                <input type="tel" id="phone" name="phone" placeholder="07XXXXXXXXX" required>
                            </div>

                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email">
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">المحافظة *</label>
                                    <select id="city" name="city" required>
                                        <option value="">اختر المحافظة</option>
                                        <option value="baghdad">بغداد</option>
                                        <option value="basra">البصرة</option>
                                        <option value="erbil">أربيل</option>
                                        <option value="mosul">الموصل</option>
                                        <option value="najaf">النجف</option>
                                        <option value="karbala">كربلاء</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="district">المنطقة *</label>
                                    <input type="text" id="district" name="district" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="address">العنوان التفصيلي *</label>
                                <textarea id="address" name="address" rows="3" placeholder="اكتب العنوان بالتفصيل..." required></textarea>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">
                                <span>💳</span>
                                طريقة الدفع
                            </h3>
                            
                            <div class="payment-methods">
                                <div class="payment-option selected" onclick="selectPayment('cash')">
                                    <input type="radio" name="payment" value="cash" checked>
                                    <div class="payment-icon">💵</div>
                                    <h4>الدفع عند الاستلام</h4>
                                    <p>ادفع نقداً عند وصول الطلب</p>
                                </div>
                                
                                <div class="payment-option" onclick="selectPayment('card')">
                                    <input type="radio" name="payment" value="card">
                                    <div class="payment-icon">💳</div>
                                    <h4>بطاقة ائتمان</h4>
                                    <p>فيزا أو ماستركارد</p>
                                </div>
                                
                                <div class="payment-option" onclick="selectPayment('bank')">
                                    <input type="radio" name="payment" value="bank">
                                    <div class="payment-icon">🏦</div>
                                    <h4>تحويل بنكي</h4>
                                    <p>تحويل مباشر للحساب</p>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">
                                <span>📝</span>
                                ملاحظات إضافية
                            </h3>
                            
                            <div class="form-group">
                                <label for="notes">ملاحظات للطلب</label>
                                <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="order-summary">
                    <h3 class="summary-title">ملخص الطلب</h3>
                    
                    <div class="order-item">
                        <div class="item-info">
                            <div class="item-image">🧁</div>
                            <div class="item-details">
                                <h4>كب كيك الفانيليا</h4>
                                <p>الكمية: 2</p>
                            </div>
                        </div>
                        <div class="item-price">30,000 د.ع</div>
                    </div>

                    <div class="order-item">
                        <div class="item-info">
                            <div class="item-image">🍰</div>
                            <div class="item-details">
                                <h4>تورتة الشوكولاتة</h4>
                                <p>الكمية: 1</p>
                            </div>
                        </div>
                        <div class="item-price">47,000 د.ع</div>
                    </div>

                    <div class="order-item">
                        <div class="item-info">
                            <div class="item-image">🍪</div>
                            <div class="item-details">
                                <h4>كوكيز اللوز</h4>
                                <p>الكمية: 1</p>
                            </div>
                        </div>
                        <div class="item-price">25,000 د.ع</div>
                    </div>
                    
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span>102,000 د.ع</span>
                    </div>
                    
                    <div class="summary-row">
                        <span>الشحن:</span>
                        <span>5,000 د.ع</span>
                    </div>
                    
                    <div class="summary-row total">
                        <span>المجموع الكلي:</span>
                        <span>107,000 د.ع</span>
                    </div>

                    <button class="btn" onclick="submitOrder()">تأكيد الطلب</button>

                    <div class="security-info">
                        <p>🔒 معلوماتك محمية بأعلى معايير الأمان</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function selectPayment(method) {
            // إزالة التحديد من جميع الخيارات
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // إضافة التحديد للخيار المختار
            event.currentTarget.classList.add('selected');
            
            // تحديد الراديو بتن
            document.querySelector(`input[value="${method}"]`).checked = true;
        }

        function submitOrder() {
            const form = document.getElementById('checkoutForm');
            
            // التحقق من صحة البيانات
            if (!form.checkValidity()) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من رقم الهاتف العراقي
            const phone = document.getElementById('phone').value;
            if (!phone.match(/^07\d{9}$/)) {
                alert('يرجى إدخال رقم هاتف عراقي صحيح (07XXXXXXXXX)');
                return;
            }

            // إرسال الطلب
            alert('جاري معالجة طلبك...');
            setTimeout(() => {
                window.location.href = 'success.html';
            }, 2000);
        }
    </script>
</body>
</html>
