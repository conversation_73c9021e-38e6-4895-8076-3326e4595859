<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسئلة الشائعة - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-header {
            background: white;
            padding: 3rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: #666;
        }

        .faq-content {
            padding: 4rem 0;
        }

        .search-box {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 1rem;
            border: 2px solid #D8BFD8;
            border-radius: 25px;
            font-size: 1rem;
            text-align: center;
        }

        .search-input:focus {
            outline: none;
            border-color: #FF69B4;
        }

        .faq-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .category-btn {
            background: white;
            border: 2px solid #D8BFD8;
            padding: 1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            font-weight: 500;
            color: #8B4B8B;
        }

        .category-btn:hover,
        .category-btn.active {
            background: #FF69B4;
            color: white;
            border-color: #FF69B4;
        }

        .faq-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #8B4B8B;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .faq-item {
            border-bottom: 1px solid #eee;
            margin-bottom: 1rem;
        }

        .faq-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .faq-question {
            background: none;
            border: none;
            width: 100%;
            text-align: right;
            padding: 1.5rem 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: #8B4B8B;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: color 0.3s;
        }

        .faq-question:hover {
            color: #FF69B4;
        }

        .faq-question.active {
            color: #FF69B4;
        }

        .faq-icon {
            font-size: 1.2rem;
            transition: transform 0.3s;
        }

        .faq-question.active .faq-icon {
            transform: rotate(180deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            padding: 0 0 0 0;
        }

        .faq-answer.active {
            max-height: 200px;
            padding: 0 0 1.5rem 0;
        }

        .faq-answer p {
            color: #666;
            line-height: 1.8;
        }

        .contact-cta {
            background: #FFE4E1;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-top: 3rem;
        }

        .contact-cta h3 {
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .contact-cta p {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .btn {
            display: inline-block;
            background: #FF69B4;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            margin: 0 0.5rem;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-outline:hover {
            background: #FF69B4;
            color: white;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .page-title {
                font-size: 2rem;
            }

            .faq-categories {
                grid-template-columns: 1fr;
            }

            .faq-section {
                padding: 1.5rem;
            }

            .btn {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">الأسئلة الشائعة</h1>
            <p class="page-subtitle">إجابات على أكثر الأسئلة شيوعاً حول منتجاتنا وخدماتنا</p>
        </div>
    </section>

    <section class="faq-content">
        <div class="container">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="ابحث في الأسئلة الشائعة..." id="searchInput">
            </div>

            <div class="faq-categories">
                <button class="category-btn active" onclick="showCategory('all')">جميع الأسئلة</button>
                <button class="category-btn" onclick="showCategory('orders')">الطلبات</button>
                <button class="category-btn" onclick="showCategory('products')">المنتجات</button>
                <button class="category-btn" onclick="showCategory('shipping')">الشحن</button>
                <button class="category-btn" onclick="showCategory('payment')">الدفع</button>
            </div>

            <div class="faq-section" data-category="orders">
                <h2 class="section-title">
                    <span>📦</span>
                    أسئلة حول الطلبات
                </h2>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>كيف يمكنني تتبع طلبي؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>يمكنك تتبع طلبك من خلال الدخول إلى حسابك والنقر على "طلباتي". ستجد رقم التتبع ومعلومات مفصلة عن حالة الطلب. كما سنرسل لك رسائل تحديث على رقم هاتفك.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل يمكنني إلغاء أو تعديل طلبي؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>يمكنك إلغاء أو تعديل طلبك خلال ساعة من تأكيد الطلب. بعد ذلك، يدخل الطلب مرحلة التحضير ولا يمكن تعديله. للإلغاء أو التعديل، اتصل بنا على 07XX XXX XXXX.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>ما هو الحد الأدنى للطلب؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>الحد الأدنى للطلب هو 20,000 دينار عراقي. للطلبات أقل من هذا المبلغ، سيتم إضافة رسوم توصيل إضافية.</p>
                    </div>
                </div>
            </div>

            <div class="faq-section" data-category="products">
                <h2 class="section-title">
                    <span>🧁</span>
                    أسئلة حول المنتجات
                </h2>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل منتجاتكم طازجة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>نعم، جميع منتجاتنا طازجة ومصنوعة يومياً. نحن لا نستخدم أي مواد حافظة ونضمن وصول المنتجات إليك في أفضل حالة.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل يمكنني طلب تصميم مخصص للتورتة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>بالطبع! نوفر خدمة التصميم المخصص للتورتات. يرجى التواصل معنا قبل 48 ساعة على الأقل من موعد التسليم المطلوب لضمان التنفيذ بأفضل جودة.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>كم مدة صلاحية المنتجات؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>تختلف مدة الصلاحية حسب نوع المنتج: الكب كيك والكوكيز 3 أيام، التورتات 2-3 أيام، المعجنات الحلوة يوم واحد. يُحفظ في درجة حرارة الغرفة أو الثلاجة حسب النوع.</p>
                    </div>
                </div>
            </div>

            <div class="faq-section" data-category="shipping">
                <h2 class="section-title">
                    <span>🚚</span>
                    أسئلة حول الشحن والتوصيل
                </h2>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>ما هي مناطق التوصيل المتاحة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>نوصل إلى جميع أنحاء العراق. التوصيل مجاني داخل بغداد للطلبات أكثر من 50,000 د.ع. رسوم التوصيل للمحافظات الأخرى تبدأ من 5,000 د.ع.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>كم يستغرق التوصيل؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>داخل بغداد: 2-4 ساعات للطلبات العادية، نفس اليوم للطلبات المستعجلة. المحافظات الأخرى: 1-2 يوم عمل. للطلبات المخصصة قد تحتاج وقت إضافي.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل يمكنني تحديد وقت التوصيل؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>نعم، يمكنك اختيار الفترة المفضلة للتوصيل (صباحية أو مسائية). للمناسبات الخاصة، يمكن تحديد وقت دقيق مقابل رسوم إضافية.</p>
                    </div>
                </div>
            </div>

            <div class="faq-section" data-category="payment">
                <h2 class="section-title">
                    <span>💳</span>
                    أسئلة حول الدفع
                </h2>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>ما هي طرق الدفع المتاحة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>نقبل الدفع عند الاستلام (نقداً)، بطاقات الائتمان (فيزا/ماستركارد)، والتحويل البنكي. جميع المعاملات آمنة ومحمية.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل معلوماتي المالية آمنة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>نعم، نستخدم أحدث تقنيات التشفير لحماية معلوماتك المالية. لا نحتفظ ببيانات البطاقات الائتمانية على خوادمنا.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <button class="faq-question" onclick="toggleAnswer(this)">
                        <span>هل يمكنني الحصول على فاتورة؟</span>
                        <span class="faq-icon">▼</span>
                    </button>
                    <div class="faq-answer">
                        <p>بالطبع، سنرسل لك فاتورة مفصلة عبر البريد الإلكتروني أو الواتساب بعد تأكيد الطلب. كما يمكنك طباعة الفاتورة من حسابك.</p>
                    </div>
                </div>
            </div>

            <div class="contact-cta">
                <h3>لم تجد إجابة لسؤالك؟</h3>
                <p>فريق خدمة العملاء جاهز لمساعدتك في أي وقت</p>
                <a href="contact.html" class="btn">اتصل بنا</a>
                <a href="tel:07XXXXXXXX" class="btn btn-outline">اتصل الآن</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function toggleAnswer(button) {
            const answer = button.nextElementSibling;
            const isActive = button.classList.contains('active');
            
            // إغلاق جميع الإجابات الأخرى
            document.querySelectorAll('.faq-question.active').forEach(q => {
                q.classList.remove('active');
                q.nextElementSibling.classList.remove('active');
            });
            
            // فتح/إغلاق الإجابة الحالية
            if (!isActive) {
                button.classList.add('active');
                answer.classList.add('active');
            }
        }

        function showCategory(category) {
            // تحديث أزرار الفئات
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // إظهار/إخفاء الأقسام
            document.querySelectorAll('.faq-section').forEach(section => {
                if (category === 'all' || section.dataset.category === category) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // البحث في الأسئلة
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question span').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
                
                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
