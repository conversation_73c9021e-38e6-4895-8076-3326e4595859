<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طرق الدفع - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.8;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .page-header {
            background: white;
            padding: 3rem 0;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            color: #666;
        }

        .payment-content {
            padding: 4rem 0;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .payment-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            text-align: center;
        }

        .payment-card:hover {
            transform: translateY(-5px);
        }

        .payment-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .payment-title {
            font-size: 1.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .payment-description {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .payment-features {
            list-style: none;
            text-align: right;
        }

        .payment-features li {
            margin-bottom: 0.5rem;
            padding-right: 1.5rem;
            position: relative;
        }

        .payment-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            position: absolute;
            right: 0;
        }

        .payment-document {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.8rem;
            color: #8B4B8B;
            margin-bottom: 2rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #FFE4E1;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .subsection {
            margin-bottom: 2rem;
        }

        .subsection-title {
            font-size: 1.3rem;
            color: #FF69B4;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .payment-list {
            list-style: none;
            margin-bottom: 1rem;
        }

        .payment-list li {
            margin-bottom: 0.8rem;
            padding-right: 1.5rem;
            position: relative;
        }

        .payment-list li:before {
            content: "💳";
            position: absolute;
            right: 0;
        }

        .security-box {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }

        .security-box h4 {
            color: #155724;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .security-box p {
            color: #155724;
            margin: 0;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .warning-box h4 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .warning-box p {
            color: #856404;
            margin: 0;
        }

        .steps-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .step-card {
            background: #FFE4E1;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: #FF69B4;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .step-title {
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .step-text {
            color: #666;
            font-size: 0.9rem;
        }

        .bank-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .bank-info h4 {
            color: #495057;
            margin-bottom: 1rem;
        }

        .bank-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .bank-detail {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .bank-detail:last-child {
            border-bottom: none;
        }

        .bank-label {
            font-weight: 500;
            color: #495057;
        }

        .bank-value {
            color: #6c757d;
            font-family: monospace;
        }

        .contact-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }

        .contact-info h3 {
            color: #28a745;
            margin-bottom: 1rem;
        }

        .contact-info p {
            color: #155724;
            margin-bottom: 0.5rem;
        }

        .btn {
            display: inline-block;
            background: #FF69B4;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            margin-top: 1rem;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .page-title {
                font-size: 2rem;
            }

            .payment-document {
                padding: 2rem 1rem;
            }

            .payment-methods {
                grid-template-columns: 1fr;
            }

            .steps-container {
                grid-template-columns: 1fr;
            }

            .bank-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1 class="page-title">طرق الدفع</h1>
            <p class="page-subtitle">خيارات دفع متنوعة وآمنة لراحتك</p>
        </div>
    </section>

    <section class="payment-content">
        <div class="container">
            <div class="payment-methods">
                <div class="payment-card">
                    <div class="payment-icon">💵</div>
                    <h3 class="payment-title">الدفع عند الاستلام</h3>
                    <p class="payment-description">ادفع نقداً عند وصول طلبك إليك</p>
                    <ul class="payment-features">
                        <li>لا حاجة لبطاقة ائتمان</li>
                        <li>فحص المنتجات قبل الدفع</li>
                        <li>متاح لجميع المناطق</li>
                        <li>الطريقة الأكثر أماناً</li>
                    </ul>
                </div>

                <div class="payment-card">
                    <div class="payment-icon">💳</div>
                    <h3 class="payment-title">البطاقات الائتمانية</h3>
                    <p class="payment-description">فيزا، ماستركارد، وبطاقات محلية</p>
                    <ul class="payment-features">
                        <li>دفع فوري وآمن</li>
                        <li>تشفير عالي الأمان</li>
                        <li>إمكانية الدفع المسبق</li>
                        <li>سجل دفع إلكتروني</li>
                    </ul>
                </div>

                <div class="payment-card">
                    <div class="payment-icon">🏦</div>
                    <h3 class="payment-title">التحويل البنكي</h3>
                    <p class="payment-description">تحويل مباشر إلى حسابنا البنكي</p>
                    <ul class="payment-features">
                        <li>للطلبات الكبيرة</li>
                        <li>تأكيد سريع</li>
                        <li>رسوم منخفضة</li>
                        <li>مناسب للشركات</li>
                    </ul>
                </div>
            </div>

            <div class="payment-document">
                <div class="security-box">
                    <h4>🔒 أمان المعاملات</h4>
                    <p>جميع معاملاتك محمية بأحدث تقنيات التشفير والأمان. نحن لا نحتفظ ببيانات بطاقتك الائتمانية على خوادمنا.</p>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <span>💵</span>
                        الدفع عند الاستلام
                    </h2>
                    
                    <div class="subsection">
                        <p>هذه هي الطريقة الأكثر شيوعاً واستخداماً من قبل عملائنا. تتيح لك هذه الطريقة فحص المنتجات والتأكد من جودتها قبل الدفع.</p>
                        
                        <h3 class="subsection-title">كيفية الدفع عند الاستلام:</h3>
                        <div class="steps-container">
                            <div class="step-card">
                                <div class="step-number">1</div>
                                <div class="step-title">اختر المنتجات</div>
                                <div class="step-text">أضف المنتجات المرغوبة إلى السلة</div>
                            </div>
                            <div class="step-card">
                                <div class="step-number">2</div>
                                <div class="step-title">أدخل العنوان</div>
                                <div class="step-text">أدخل عنوان التوصيل ومعلومات الاتصال</div>
                            </div>
                            <div class="step-card">
                                <div class="step-number">3</div>
                                <div class="step-title">اختر الدفع عند الاستلام</div>
                                <div class="step-text">حدد هذا الخيار كطريقة الدفع المفضلة</div>
                            </div>
                            <div class="step-card">
                                <div class="step-number">4</div>
                                <div class="step-title">استلم وادفع</div>
                                <div class="step-text">افحص المنتجات وادفع المبلغ نقداً</div>
                            </div>
                        </div>

                        <ul class="payment-list">
                            <li>تأكد من توفر المبلغ الصحيح نقداً</li>
                            <li>يمكنك فحص المنتجات قبل الدفع</li>
                            <li>في حالة عدم الرضا، يمكن رفض الطلب</li>
                            <li>احتفظ بالفاتورة للضمان</li>
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <span>💳</span>
                        البطاقات الائتمانية
                    </h2>
                    
                    <div class="subsection">
                        <p>نقبل جميع أنواع البطاقات الائتمانية الرئيسية مع ضمان أعلى مستويات الأمان.</p>
                        
                        <h3 class="subsection-title">البطاقات المقبولة:</h3>
                        <ul class="payment-list">
                            <li>فيزا (Visa)</li>
                            <li>ماستركارد (Mastercard)</li>
                            <li>البطاقات المحلية العراقية</li>
                            <li>بطاقات الخصم المباشر</li>
                        </ul>

                        <h3 class="subsection-title">مميزات الدفع بالبطاقة:</h3>
                        <ul class="payment-list">
                            <li>دفع فوري وتأكيد سريع للطلب</li>
                            <li>إمكانية الدفع المسبق للطلبات المخصصة</li>
                            <li>سجل إلكتروني لجميع معاملاتك</li>
                            <li>حماية من الاحتيال</li>
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <span>🏦</span>
                        التحويل البنكي
                    </h2>
                    
                    <div class="subsection">
                        <p>مناسب للطلبات الكبيرة والشركات. يرجى التحويل إلى الحساب التالي:</p>
                        
                        <div class="bank-info">
                            <h4>معلومات الحساب البنكي</h4>
                            <div class="bank-details">
                                <div class="bank-detail">
                                    <span class="bank-label">اسم البنك:</span>
                                    <span class="bank-value">بنك بغداد</span>
                                </div>
                                <div class="bank-detail">
                                    <span class="bank-label">اسم الحساب:</span>
                                    <span class="bank-value">فيلا الحلويات</span>
                                </div>
                                <div class="bank-detail">
                                    <span class="bank-label">رقم الحساب:</span>
                                    <span class="bank-value">************</span>
                                </div>
                                <div class="bank-detail">
                                    <span class="bank-label">رمز البنك:</span>
                                    <span class="bank-value">BBIQ</span>
                                </div>
                            </div>
                        </div>

                        <h3 class="subsection-title">خطوات التحويل البنكي:</h3>
                        <ul class="payment-list">
                            <li>قم بالتحويل إلى الحساب المذكور أعلاه</li>
                            <li>أرسل صورة من إيصال التحويل عبر الواتساب</li>
                            <li>اذكر رقم طلبك في البيان</li>
                            <li>سنتصل بك لتأكيد استلام المبلغ</li>
                        </ul>
                    </div>
                </div>

                <div class="warning-box">
                    <h4>تنبيه مهم</h4>
                    <p>للتحويل البنكي، يجب تأكيد الدفع قبل بدء تحضير الطلب. مدة التأكيد عادة من 2-4 ساعات عمل.</p>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <span>🔒</span>
                        أمان المعاملات
                    </h2>
                    
                    <div class="subsection">
                        <ul class="payment-list">
                            <li>تشفير SSL 256-bit لجميع المعاملات</li>
                            <li>عدم حفظ بيانات البطاقات على خوادمنا</li>
                            <li>مراقبة مستمرة للأنشطة المشبوهة</li>
                            <li>التزام كامل بمعايير PCI DSS</li>
                            <li>حماية من الاحتيال والسرقة</li>
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <span>📋</span>
                        سياسة الاسترداد
                    </h2>
                    
                    <div class="subsection">
                        <ul class="payment-list">
                            <li>استرداد كامل في حالة عدم توفر المنتج</li>
                            <li>استرداد أو استبدال في حالة وجود عيب</li>
                            <li>مدة الاسترداد: 3-7 أيام عمل</li>
                            <li>الاسترداد بنفس طريقة الدفع الأصلية</li>
                        </ul>
                    </div>
                </div>

                <div class="contact-info">
                    <h3>هل تحتاج مساعدة في الدفع؟</h3>
                    <p>فريق خدمة العملاء جاهز لمساعدتك في أي استفسار حول الدفع</p>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>⏰ متاح طوال أوقات العمل</p>
                    <a href="contact.html" class="btn">اتصل بنا</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
