<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المنتج - فيلا الحلويات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .breadcrumb {
            background: white;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .breadcrumb a {
            color: #8B4B8B;
            text-decoration: none;
            margin-left: 0.5rem;
        }

        .breadcrumb a:hover {
            color: #FF69B4;
        }

        .product-details {
            background: white;
            padding: 3rem 0;
        }

        .product-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: start;
        }

        .product-gallery {
            position: sticky;
            top: 2rem;
        }

        .main-image {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8rem;
            color: white;
            margin-bottom: 1rem;
        }

        .thumbnail-images {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #FFB6C1, #D8BFD8);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .thumbnail:hover {
            transform: scale(1.1);
        }

        .product-info {
            padding: 1rem 0;
        }

        .product-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .stars {
            color: #FFD700;
            font-size: 1.2rem;
        }

        .rating-text {
            color: #666;
        }

        .product-price {
            font-size: 2rem;
            color: #FF69B4;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .product-description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .product-options {
            margin-bottom: 2rem;
        }

        .option-group {
            margin-bottom: 1.5rem;
        }

        .option-label {
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
            display: block;
        }

        .option-select {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
        }

        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #D8BFD8;
            background: white;
            border-radius: 50%;
            font-size: 1.2rem;
            font-weight: bold;
            color: #8B4B8B;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quantity-btn:hover {
            background: #FF69B4;
            color: white;
            border-color: #FF69B4;
        }

        .quantity-input {
            width: 80px;
            text-align: center;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 10px;
            font-size: 1rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            flex: 1;
            min-width: 200px;
        }

        .btn-primary {
            background: #FF69B4;
            color: white;
        }

        .btn-primary:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #FF69B4;
            border: 2px solid #FF69B4;
        }

        .btn-secondary:hover {
            background: #FF69B4;
            color: white;
        }

        .product-features {
            background: #FFE4E1;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .features-list {
            list-style: none;
        }

        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #D8BFD8;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li:before {
            content: "✓";
            color: #FF69B4;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .related-products {
            background: #FFE4E1;
            padding: 3rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            color: #8B4B8B;
            margin-bottom: 2rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .card-image {
            height: 150px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
        }

        .card-info {
            padding: 1rem;
        }

        .card-title {
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .card-price {
            color: #FF69B4;
            font-weight: bold;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .product-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .product-gallery {
                position: static;
            }

            .main-image {
                height: 300px;
                font-size: 6rem;
            }

            .product-title {
                font-size: 2rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="breadcrumb">
        <div class="container">
            <a href="index.html">الرئيسية</a> / 
            <a href="products.html">المنتجات</a> / 
            <span>كب كيك الفانيليا</span>
        </div>
    </section>

    <section class="product-details">
        <div class="container">
            <div class="product-container">
                <div class="product-gallery">
                    <div class="main-image">🧁</div>
                    <div class="thumbnail-images">
                        <div class="thumbnail">🧁</div>
                        <div class="thumbnail">🧁</div>
                        <div class="thumbnail">🧁</div>
                        <div class="thumbnail">🧁</div>
                    </div>
                </div>

                <div class="product-info">
                    <h1 class="product-title">كب كيك الفانيليا الفاخر</h1>
                    
                    <div class="product-rating">
                        <div class="stars">★★★★★</div>
                        <span class="rating-text">(24 تقييم)</span>
                    </div>

                    <div class="product-price">15,000 د.ع</div>

                    <div class="product-description">
                        <p>كب كيك طري ولذيذ بنكهة الفانيليا الطبيعية، مصنوع من أجود المكونات الطبيعية. يتميز بقوامه الهش والطري وطعمه الرائع الذي يذوب في الفم. مغطى بكريمة الزبدة الفاخرة ومزين بلمسات فنية جميلة.</p>
                        
                        <p>مثالي للمناسبات الخاصة أو كوجبة خفيفة لذيذة مع القهوة أو الشاي. يُحفظ طازجاً لمدة 3 أيام في درجة حرارة الغرفة.</p>
                    </div>

                    <div class="product-options">
                        <div class="option-group">
                            <label class="option-label">الحجم:</label>
                            <select class="option-select">
                                <option value="small">صغير (قطعة واحدة)</option>
                                <option value="medium">متوسط (4 قطع)</option>
                                <option value="large">كبير (6 قطع)</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label class="option-label">نوع التغليف:</label>
                            <select class="option-select">
                                <option value="standard">تغليف عادي</option>
                                <option value="gift">تغليف هدايا (+2,000 د.ع)</option>
                                <option value="premium">تغليف فاخر (+5,000 د.ع)</option>
                            </select>
                        </div>
                    </div>

                    <div class="quantity-selector">
                        <label class="option-label">الكمية:</label>
                        <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                        <input type="number" class="quantity-input" value="1" min="1" id="quantity">
                        <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="addToCart()">أضف إلى السلة</button>
                        <button class="btn btn-secondary">أضف إلى المفضلة</button>
                    </div>

                    <div class="product-features">
                        <h3 style="color: #8B4B8B; margin-bottom: 1rem;">مميزات المنتج:</h3>
                        <ul class="features-list">
                            <li>مصنوع من مكونات طبيعية 100%</li>
                            <li>خالي من المواد الحافظة الضارة</li>
                            <li>طازج يومياً</li>
                            <li>تغليف صحي وآمن</li>
                            <li>مناسب للأطفال والكبار</li>
                            <li>يمكن تخصيص الطلب</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="related-products">
        <div class="container">
            <h2 class="section-title">منتجات مشابهة</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="card-image">🍰</div>
                    <div class="card-info">
                        <h3 class="card-title">تورتة الشوكولاتة</h3>
                        <p class="card-price">45,000 د.ع</p>
                    </div>
                </div>
                <div class="product-card">
                    <div class="card-image">🍪</div>
                    <div class="card-info">
                        <h3 class="card-title">كوكيز اللوز</h3>
                        <p class="card-price">25,000 د.ع</p>
                    </div>
                </div>
                <div class="product-card">
                    <div class="card-image">🥧</div>
                    <div class="card-info">
                        <h3 class="card-title">تارت الفراولة</h3>
                        <p class="card-price">35,000 د.ع</p>
                    </div>
                </div>
                <div class="product-card">
                    <div class="card-image">🍩</div>
                    <div class="card-info">
                        <h3 class="card-title">دونات محشوة</h3>
                        <p class="card-price">12,000 د.ع</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function increaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            quantityInput.value = parseInt(quantityInput.value) + 1;
        }

        function decreaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            if (parseInt(quantityInput.value) > 1) {
                quantityInput.value = parseInt(quantityInput.value) - 1;
            }
        }

        function addToCart() {
            alert('تم إضافة المنتج إلى السلة بنجاح!');
        }
    </script>
</body>
</html>
