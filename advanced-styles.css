/* فيلا الحلويات - الأنماط المتقدمة والتأثيرات الخاصة */

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg) scale(0.9);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(212, 165, 116, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(212, 165, 116, 0.6);
    }
}

/* Hero Section Advanced Styles */
.hero-advanced {
    position: relative;
    overflow: hidden;
}

.hero-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(212, 165, 116, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(222, 219, 186, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(145, 96, 69, 0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--warm-gold);
    border-radius: 50%;
    opacity: 0.3;
    animation: float 8s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { top: 40%; left: 80%; animation-delay: 4s; }
.particle:nth-child(4) { top: 80%; left: 70%; animation-delay: 6s; }

/* Product Cards Advanced Effects */
.product-card-advanced {
    position: relative;
    overflow: hidden;
}

.product-card-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.6s;
}

.product-card-advanced:hover::before {
    left: 100%;
}

.product-image-advanced {
    position: relative;
    overflow: hidden;
}

.product-image-advanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(145, 96, 69, 0.1) 0%,
        transparent 50%,
        rgba(212, 165, 116, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card-advanced:hover .product-image-advanced::after {
    opacity: 1;
}

/* Button Advanced Effects */
.btn-advanced {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
}

.btn-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s;
}

.btn-advanced:hover::before {
    left: 100%;
}

.btn-glow {
    animation: glow 2s ease-in-out infinite;
}

/* Form Advanced Styles */
.form-group-advanced {
    position: relative;
    margin-bottom: 2rem;
}

.form-label-floating {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: var(--text-light);
    transition: all 0.3s ease;
    pointer-events: none;
    background: white;
    padding: 0 0.5rem;
}

.form-input-advanced:focus + .form-label-floating,
.form-input-advanced:not(:placeholder-shown) + .form-label-floating {
    top: -0.5rem;
    font-size: 0.85rem;
    color: var(--warm-gold);
    font-weight: 600;
}

.form-input-advanced {
    border: 2px solid var(--light-beige);
    transition: all 0.3s ease;
}

.form-input-advanced:focus {
    border-color: var(--warm-gold);
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-beige);
    border-top: 4px solid var(--warm-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    background: linear-gradient(135deg, var(--success-green) 0%, #4CAF50 100%);
}

.notification-warning {
    background: linear-gradient(135deg, var(--warning-orange) 0%, #FF9800 100%);
}

.notification-error {
    background: linear-gradient(135deg, var(--error-red) 0%, #F44336 100%);
}

.notification-info {
    background: linear-gradient(135deg, var(--info-blue) 0%, #2196F3 100%);
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Advanced Grid Layouts */
.masonry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    grid-auto-rows: 10px;
    gap: 20px;
}

.masonry-item {
    grid-row-end: span var(--row-span);
}

/* Glassmorphism Effects */
.glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Neumorphism Effects */
.neuro-card {
    background: var(--cream);
    border-radius: 20px;
    box-shadow: 
        20px 20px 60px rgba(145, 96, 69, 0.1),
        -20px -20px 60px rgba(255, 255, 255, 0.8);
}

.neuro-button {
    background: var(--cream);
    border: none;
    border-radius: 15px;
    box-shadow: 
        8px 8px 16px rgba(145, 96, 69, 0.1),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.neuro-button:hover {
    box-shadow: 
        4px 4px 8px rgba(145, 96, 69, 0.1),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.neuro-button:active {
    box-shadow: 
        inset 4px 4px 8px rgba(145, 96, 69, 0.1),
        inset -4px -4px 8px rgba(255, 255, 255, 0.8);
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
