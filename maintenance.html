<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموقع قيد الصيانة - فيلا الحلويات</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .maintenance-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem 0;
        }

        .maintenance-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .maintenance-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 105, 180, 0.1), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .maintenance-icon {
            font-size: 6rem;
            margin-bottom: 1.5rem;
            animation: rotate 4s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .maintenance-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .maintenance-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .countdown-container {
            background: #FFE4E1;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .countdown-title {
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .countdown {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .countdown-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .countdown-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FF69B4;
            margin-bottom: 0.5rem;
        }

        .countdown-label {
            color: #8B4B8B;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .progress-bar {
            background: #D8BFD8;
            border-radius: 10px;
            height: 8px;
            margin: 1rem 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #FF69B4, #FFB6C1);
            height: 100%;
            border-radius: 10px;
            animation: progress 2s ease-in-out infinite alternate;
        }

        @keyframes progress {
            0% {
                width: 30%;
            }
            100% {
                width: 70%;
            }
        }

        .features-preview {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .features-title {
            color: #28a745;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .features-list {
            list-style: none;
            text-align: right;
        }

        .features-list li {
            margin-bottom: 0.8rem;
            padding-right: 1.5rem;
            position: relative;
            color: #155724;
        }

        .features-list li:before {
            content: "✨";
            position: absolute;
            right: 0;
        }

        .contact-info {
            background: #fff3cd;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .contact-title {
            color: #856404;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .contact-details {
            color: #856404;
        }

        .contact-details p {
            margin-bottom: 0.5rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .social-link {
            width: 50px;
            height: 50px;
            background: #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            transition: all 0.3s;
        }

        .social-link:hover {
            background: #FF1493;
            transform: translateY(-3px) scale(1.1);
        }

        .newsletter {
            background: #FFE4E1;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .newsletter-title {
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .newsletter-form {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .newsletter-input {
            flex: 1;
            padding: 0.8rem;
            border: 2px solid #D8BFD8;
            border-radius: 25px;
            font-size: 1rem;
            min-width: 200px;
        }

        .newsletter-input:focus {
            outline: none;
            border-color: #FF69B4;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            background: #FF69B4;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            font-size: 3rem;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .floating-element:nth-child(4) {
            bottom: 20%;
            right: 10%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-30px) rotate(180deg);
            }
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .maintenance-card {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .maintenance-title {
                font-size: 2rem;
            }

            .maintenance-icon {
                font-size: 4rem;
            }

            .countdown {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .countdown-number {
                font-size: 1.5rem;
            }

            .newsletter-form {
                flex-direction: column;
            }

            .newsletter-input {
                min-width: auto;
            }

            .social-links {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element">🧁</div>
        <div class="floating-element">🍰</div>
        <div class="floating-element">🍪</div>
        <div class="floating-element">🥧</div>
    </div>

    <section class="maintenance-content">
        <div class="container">
            <div class="maintenance-card">
                <div class="maintenance-icon">⚙️</div>
                <h1 class="maintenance-title">الموقع قيد الصيانة</h1>
                <p class="maintenance-message">
                    نعتذر عن الإزعاج! نحن نعمل حالياً على تحسين موقعنا لنقدم لك تجربة أفضل.
                    سنعود قريباً بمميزات جديدة ومثيرة!
                </p>

                <div class="countdown-container">
                    <h3 class="countdown-title">العودة المتوقعة خلال:</h3>
                    <div class="countdown">
                        <div class="countdown-item">
                            <div class="countdown-number" id="hours">02</div>
                            <div class="countdown-label">ساعة</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-number" id="minutes">30</div>
                            <div class="countdown-label">دقيقة</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-number" id="seconds">45</div>
                            <div class="countdown-label">ثانية</div>
                        </div>
                        <div class="countdown-item">
                            <div class="countdown-number">95%</div>
                            <div class="countdown-label">مكتمل</div>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>

                <div class="features-preview">
                    <h3 class="features-title">ما الجديد الذي ننتظره؟</h3>
                    <ul class="features-list">
                        <li>تصميم جديد ومحسن للموقع</li>
                        <li>منتجات جديدة ومميزة</li>
                        <li>نظام طلبات أسرع وأسهل</li>
                        <li>خيارات دفع إضافية</li>
                        <li>تتبع أفضل للطلبات</li>
                        <li>عروض وخصومات حصرية</li>
                    </ul>
                </div>

                <div class="newsletter">
                    <h3 class="newsletter-title">اشترك ليصلك إشعار عند العودة</h3>
                    <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
                        <input type="email" class="newsletter-input" placeholder="أدخل بريدك الإلكتروني" required>
                        <button type="submit" class="btn">اشتراك</button>
                    </form>
                </div>

                <div class="contact-info">
                    <h3 class="contact-title">هل تحتاج مساعدة عاجلة؟</h3>
                    <div class="contact-details">
                        <p>📞 للطوارئ: 07XX XXX XXXX</p>
                        <p>📧 البريد الإلكتروني: <EMAIL></p>
                        <p>⏰ خدمة العملاء: 9:00 ص - 6:00 م</p>
                    </div>
                </div>

                <div class="social-links">
                    <a href="#" class="social-link" title="فيسبوك">📘</a>
                    <a href="#" class="social-link" title="إنستغرام">📷</a>
                    <a href="#" class="social-link" title="تويتر">🐦</a>
                    <a href="#" class="social-link" title="واتساب">📱</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        // العد التنازلي
        function updateCountdown() {
            // تحديد وقت انتهاء الصيانة (مثال: بعد 2.5 ساعة من الآن)
            const endTime = new Date().getTime() + (2.5 * 60 * 60 * 1000);
            
            function update() {
                const now = new Date().getTime();
                const timeLeft = endTime - now;
                
                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
                } else {
                    // انتهت الصيانة، إعادة توجيه للصفحة الرئيسية
                    window.location.href = 'index.html';
                }
            }
            
            update();
            setInterval(update, 1000);
        }

        // اشتراك في النشرة الإخبارية
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            // محاكاة إرسال البيانات
            alert(`شكراً لك! تم تسجيل بريدك الإلكتروني: ${email}\nسنرسل لك إشعاراً عند عودة الموقع.`);
            event.target.reset();
        }

        // إنشاء حلويات عائمة
        function createFloatingSweets() {
            const sweetIcons = ['🧁', '🍰', '🍪', '🥧', '🍩', '🎂', '🧁', '🍰'];
            
            setInterval(() => {
                const sweet = document.createElement('div');
                sweet.textContent = sweetIcons[Math.floor(Math.random() * sweetIcons.length)];
                sweet.style.position = 'fixed';
                sweet.style.left = Math.random() * 100 + 'vw';
                sweet.style.top = '-50px';
                sweet.style.fontSize = '2rem';
                sweet.style.opacity = '0.2';
                sweet.style.pointerEvents = 'none';
                sweet.style.zIndex = '-1';
                sweet.style.animation = 'fall 10s linear forwards';
                
                document.body.appendChild(sweet);
                
                setTimeout(() => {
                    sweet.remove();
                }, 10000);
            }, 2000);
        }

        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // تشغيل الوظائف
        updateCountdown();
        createFloatingSweets();

        // تحديث شريط التقدم
        function updateProgressBar() {
            const progressFill = document.querySelector('.progress-fill');
            let width = 30;
            
            setInterval(() => {
                width += Math.random() * 2;
                if (width > 95) width = 95;
                progressFill.style.width = width + '%';
            }, 5000);
        }

        updateProgressBar();

        // رسالة في وحدة التحكم
        console.log('🍰 فيلا الحلويات - الموقع قيد الصيانة');
        console.log('نعمل بجد لنقدم لك تجربة أفضل!');
    </script>
</body>
</html>
