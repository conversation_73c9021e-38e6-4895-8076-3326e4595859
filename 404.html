<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - فيلا الحلويات</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        .error-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem 0;
        }

        .error-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .error-animation {
            font-size: 8rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #FF69B4;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .error-title {
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .error-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .search-box {
            background: #FFE4E1;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .search-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #D8BFD8;
            border-radius: 25px;
            font-size: 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .search-input:focus {
            outline: none;
            border-color: #FF69B4;
        }

        .suggestions {
            margin: 2rem 0;
        }

        .suggestions h3 {
            color: #8B4B8B;
            margin-bottom: 1rem;
        }

        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .suggestion-item {
            background: #FFE4E1;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            transition: transform 0.3s;
            text-decoration: none;
            color: #8B4B8B;
        }

        .suggestion-item:hover {
            transform: translateY(-3px);
            color: #FF69B4;
        }

        .suggestion-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .suggestion-text {
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 180px;
        }

        .btn-primary {
            background: #FF69B4;
            color: white;
        }

        .btn-primary:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #8B4B8B;
            border: 2px solid #8B4B8B;
        }

        .btn-secondary:hover {
            background: #8B4B8B;
            color: white;
        }

        .help-section {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }

        .help-section h4 {
            color: #28a745;
            margin-bottom: 1rem;
        }

        .help-section p {
            color: #155724;
            margin-bottom: 0.5rem;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(4) {
            bottom: 20%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .error-card {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .error-code {
                font-size: 4rem;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-animation {
                font-size: 6rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .suggestions-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element">🧁</div>
        <div class="floating-element">🍰</div>
        <div class="floating-element">🍪</div>
        <div class="floating-element">🥧</div>
    </div>

    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
            </div>
        </div>
    </header>

    <section class="error-content">
        <div class="container">
            <div class="error-card">
                <div class="error-animation">😕</div>
                <div class="error-code">404</div>
                <h1 class="error-title">عذراً، الصفحة غير موجودة!</h1>
                <p class="error-message">
                    يبدو أن الصفحة التي تبحث عنها قد تم نقلها أو حذفها أو أن الرابط غير صحيح.
                    لا تقلق، يمكنك العثور على ما تبحث عنه من خلال الخيارات أدناه.
                </p>

                <div class="search-box">
                    <input type="text" class="search-input" placeholder="ابحث عن المنتجات أو الصفحات..." id="searchInput">
                    <button class="btn btn-primary" onclick="performSearch()">بحث</button>
                </div>

                <div class="suggestions">
                    <h3>صفحات مقترحة:</h3>
                    <div class="suggestions-grid">
                        <a href="index.html" class="suggestion-item">
                            <div class="suggestion-icon">🏠</div>
                            <div class="suggestion-text">الصفحة الرئيسية</div>
                        </a>
                        <a href="products.html" class="suggestion-item">
                            <div class="suggestion-icon">🧁</div>
                            <div class="suggestion-text">جميع المنتجات</div>
                        </a>
                        <a href="about.html" class="suggestion-item">
                            <div class="suggestion-icon">ℹ️</div>
                            <div class="suggestion-text">من نحن</div>
                        </a>
                        <a href="contact.html" class="suggestion-item">
                            <div class="suggestion-icon">📞</div>
                            <div class="suggestion-text">اتصل بنا</div>
                        </a>
                    </div>
                </div>

                <div class="action-buttons">
                    <a href="index.html" class="btn btn-primary">العودة للرئيسية</a>
                    <button class="btn btn-secondary" onclick="goBack()">الصفحة السابقة</button>
                </div>

                <div class="help-section">
                    <h4>هل تحتاج مساعدة؟</h4>
                    <p>إذا كنت تواجه مشكلة مستمرة، لا تتردد في التواصل معنا</p>
                    <p>📞 07XX XXX XXXX | 📧 <EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }

        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (searchTerm) {
                // في التطبيق الحقيقي، هذا سيوجه إلى صفحة البحث
                window.location.href = `products.html?search=${encodeURIComponent(searchTerm)}`;
            } else {
                alert('يرجى إدخال كلمة للبحث');
            }
        }

        // البحث عند الضغط على Enter
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // إضافة تأثير الحركة للعناصر العائمة
        function createFloatingSweets() {
            const sweetIcons = ['🧁', '🍰', '🍪', '🥧', '🍩', '🎂'];
            
            setInterval(() => {
                const sweet = document.createElement('div');
                sweet.textContent = sweetIcons[Math.floor(Math.random() * sweetIcons.length)];
                sweet.style.position = 'fixed';
                sweet.style.left = Math.random() * 100 + 'vw';
                sweet.style.top = '-50px';
                sweet.style.fontSize = '2rem';
                sweet.style.opacity = '0.3';
                sweet.style.pointerEvents = 'none';
                sweet.style.zIndex = '-1';
                sweet.style.animation = 'fall 8s linear forwards';
                
                document.body.appendChild(sweet);
                
                setTimeout(() => {
                    sweet.remove();
                }, 8000);
            }, 3000);
        }

        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // تشغيل الحلويات العائمة
        createFloatingSweets();

        // تتبع الأخطاء (في التطبيق الحقيقي)
        console.log('404 Error - Page not found:', window.location.href);
    </script>
</body>
</html>
