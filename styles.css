/* فيلا الحلويات - ملف الأنماط الرئيسي */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-brown: #916045;
    --light-beige: #DEDIBA;
    --warm-gold: #D4A574;
    --cream: #F5F1E8;
    --dark-brown: #6B4423;
    --accent-gold: #B8956A;
    --text-dark: #2C1810;
    --text-light: #8B7355;
    --success-green: #2D5A27;
    --warning-orange: #B8860B;
    --error-red: #8B4513;
    --info-blue: #4A6741;
}

body {
    font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    background: linear-gradient(135deg, var(--cream) 0%, var(--light-beige) 50%, #F8F5F0 100%);
    color: var(--text-dark);
    line-height: 1.7;
    font-weight: 400;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
    padding: 1.2rem 0;
    box-shadow: 0 8px 32px rgba(145, 96, 69, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.logo {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--cream);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    color: var(--warm-gold);
}

nav ul {
    list-style: none;
    display: flex;
    gap: 2.5rem;
    flex-wrap: wrap;
    align-items: center;
}

nav a {
    text-decoration: none;
    color: var(--cream);
    font-weight: 500;
    font-size: 1.05rem;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--warm-gold);
    transition: width 0.3s ease;
}

nav a:hover {
    color: var(--warm-gold);
}

nav a:hover::after {
    width: 100%;
}

.cart-icon {
    background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-icon:hover {
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 165, 116, 0.4);
}

/* Button Styles */
.btn {
    display: inline-block;
    background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.05rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.3);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(212, 165, 116, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary-brown);
    border: 2px solid var(--primary-brown);
}

.btn-outline:hover {
    background: var(--primary-brown);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--text-light) 0%, var(--primary-brown) 100%);
}

/* Card Styles */
.card {
    background: white;
    border-radius: 24px;
    padding: 2rem;
    box-shadow: 0 15px 40px rgba(145, 96, 69, 0.1);
    border: 1px solid rgba(212, 165, 116, 0.15);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(145, 96, 69, 0.15);
}

/* Section Titles */
.section-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-brown);
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
    width: 100%;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
    border-radius: 2px;
}

/* Form Styles */
input, select, textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--light-beige);
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--warm-gold);
    box-shadow: 0 8px 25px rgba(212, 165, 116, 0.2);
    transform: translateY(-2px);
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
    color: var(--cream);
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%23D4A574" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="%23DEDIBA" opacity="0.08"/></svg>');
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: var(--warm-gold);
    font-size: 1.3rem;
    font-weight: 700;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--warm-gold);
    border-radius: 1px;
}

.footer-section a {
    color: var(--cream);
    text-decoration: none;
    display: block;
    margin-bottom: 0.8rem;
    transition: all 0.3s ease;
    padding: 0.3rem 0;
    position: relative;
    font-weight: 400;
}

.footer-section a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 1px;
    background: var(--warm-gold);
    transition: width 0.3s ease;
}

.footer-section a:hover {
    color: var(--warm-gold);
    padding-left: 1rem;
}

.footer-section a:hover::before {
    width: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    nav ul {
        justify-content: center;
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .logo {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }
}
