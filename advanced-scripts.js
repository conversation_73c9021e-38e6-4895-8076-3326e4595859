// فيلا الحلويات - السكريبت المتقدم والتفاعلات الذكية

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق الرئيسية
function initializeApp() {
    initScrollAnimations();
    initParticleEffects();
    initSmartNotifications();
    initAdvancedInteractions();
    initPerformanceOptimizations();
    initAccessibilityFeatures();
}

// تأثيرات التمرير المتقدمة
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // إضافة تأخير متدرج للعناصر المتعددة
                const siblings = entry.target.parentElement.children;
                Array.from(siblings).forEach((sibling, index) => {
                    if (sibling.classList.contains('scroll-reveal')) {
                        setTimeout(() => {
                            sibling.classList.add('revealed');
                        }, index * 100);
                    }
                });
            }
        });
    }, observerOptions);

    // مراقبة جميع العناصر القابلة للكشف
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });
}

// تأثيرات الجسيمات المتحركة
function initParticleEffects() {
    const heroSection = document.querySelector('.hero');
    if (!heroSection) return;

    // إنشاء حاوية الجسيمات
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'hero-particles';
    heroSection.appendChild(particlesContainer);

    // إنشاء الجسيمات
    for (let i = 0; i < 20; i++) {
        createParticle(particlesContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    // موضع عشوائي
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    
    // حجم عشوائي
    const size = Math.random() * 6 + 2;
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';
    
    // تأخير عشوائي للحركة
    particle.style.animationDelay = Math.random() * 8 + 's';
    particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
    
    container.appendChild(particle);
    
    // إعادة إنشاء الجسيم بعد انتهاء الحركة
    setTimeout(() => {
        if (particle.parentElement) {
            particle.remove();
            createParticle(container);
        }
    }, (Math.random() * 4 + 6) * 1000);
}

// نظام الإشعارات الذكية
function initSmartNotifications() {
    window.showNotification = function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // إضافة أيقونة حسب النوع
        const icon = getNotificationIcon(type);
        notification.innerHTML = `${icon} ${message}`;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
        
        // إزالة عند النقر
        notification.addEventListener('click', () => {
            notification.remove();
        });
    };
}

function getNotificationIcon(type) {
    const icons = {
        success: '✅',
        warning: '⚠️',
        error: '❌',
        info: 'ℹ️'
    };
    return icons[type] || icons.info;
}

// التفاعلات المتقدمة
function initAdvancedInteractions() {
    // تأثير التمرير السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // تأثيرات التمرير على البطاقات
    document.querySelectorAll('.product-card, .feature-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 25px 60px rgba(145, 96, 69, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 15px 40px rgba(145, 96, 69, 0.1)';
        });
    });

    // تأثير الكتابة المتحركة
    const typewriterElements = document.querySelectorAll('.typewriter');
    typewriterElements.forEach(element => {
        typewriterEffect(element);
    });
}

function typewriterEffect(element) {
    const text = element.textContent;
    element.textContent = '';
    element.style.borderRight = '2px solid var(--warm-gold)';
    
    let i = 0;
    const timer = setInterval(() => {
        element.textContent += text.charAt(i);
        i++;
        
        if (i >= text.length) {
            clearInterval(timer);
            setTimeout(() => {
                element.style.borderRight = 'none';
            }, 1000);
        }
    }, 100);
}

// تحسينات الأداء
function initPerformanceOptimizations() {
    // تحميل الصور بشكل تدريجي
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // تحسين الأداء للتمرير
    let ticking = false;
    function updateScrollEffects() {
        // تأثيرات التمرير المخصصة
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
        
        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });
}

// ميزات إمكانية الوصول
function initAccessibilityFeatures() {
    // دعم التنقل بلوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });

    // تحسين التباين للمستخدمين ذوي الاحتياجات الخاصة
    const highContrastToggle = document.createElement('button');
    highContrastToggle.textContent = 'تباين عالي';
    highContrastToggle.className = 'accessibility-toggle';
    highContrastToggle.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 10000;
        padding: 0.5rem;
        background: var(--primary-brown);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 0.8rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.3s;
    `;

    highContrastToggle.addEventListener('click', function() {
        document.body.classList.toggle('high-contrast');
    });

    document.body.appendChild(highContrastToggle);
}

// وظائف مساعدة للتفاعل مع عربة التسوق
window.CartManager = {
    items: JSON.parse(localStorage.getItem('cartItems') || '[]'),
    
    addItem: function(product) {
        const existingItem = this.items.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({...product, quantity: 1});
        }
        
        this.saveCart();
        this.updateCartDisplay();
        showNotification('تم إضافة المنتج إلى السلة', 'success');
    },
    
    removeItem: function(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartDisplay();
        showNotification('تم حذف المنتج من السلة', 'info');
    },
    
    updateQuantity: function(productId, quantity) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            item.quantity = Math.max(0, quantity);
            if (item.quantity === 0) {
                this.removeItem(productId);
            } else {
                this.saveCart();
                this.updateCartDisplay();
            }
        }
    },
    
    getTotal: function() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    },
    
    getItemCount: function() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    },
    
    saveCart: function() {
        localStorage.setItem('cartItems', JSON.stringify(this.items));
    },
    
    updateCartDisplay: function() {
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon) {
            const count = this.getItemCount();
            cartIcon.textContent = `🛒 السلة (${count})`;
        }
    }
};

// تهيئة عربة التسوق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    CartManager.updateCartDisplay();
});

// وظائف البحث المتقدم
window.SearchManager = {
    init: function() {
        const searchInputs = document.querySelectorAll('.search-box, .search-input');
        searchInputs.forEach(input => {
            input.addEventListener('input', this.handleSearch.bind(this));
        });
    },
    
    handleSearch: function(e) {
        const query = e.target.value.toLowerCase();
        const products = document.querySelectorAll('.product-card');
        
        products.forEach(product => {
            const title = product.querySelector('.product-title')?.textContent.toLowerCase() || '';
            const description = product.querySelector('.product-description')?.textContent.toLowerCase() || '';
            
            if (title.includes(query) || description.includes(query)) {
                product.style.display = 'block';
                product.style.animation = 'fadeInUp 0.3s ease';
            } else {
                product.style.display = 'none';
            }
        });
    }
};

// تهيئة البحث
document.addEventListener('DOMContentLoaded', function() {
    SearchManager.init();
});
