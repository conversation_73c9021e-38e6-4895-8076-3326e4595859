<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيلا الحلويات - متجر الحلويات الأول</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFFFFF 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #D8BFD8;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #8B4B8B;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        nav a {
            text-decoration: none;
            color: #8B4B8B;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #FF69B4;
        }

        .cart-icon {
            background: #FF69B4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cart-icon:hover {
            background: #FF1493;
        }

        .hero {
            background: linear-gradient(rgba(255,182,193,0.8), rgba(255,182,193,0.8)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23FFE4E1" width="1200" height="600"/><circle fill="%23D8BFD8" cx="200" cy="150" r="50" opacity="0.3"/><circle fill="%23FF69B4" cx="800" cy="300" r="80" opacity="0.2"/><circle fill="%23DDA0DD" cx="1000" cy="100" r="60" opacity="0.3"/></svg>');
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .btn {
            display: inline-block;
            background: #FF69B4;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .features {
            padding: 4rem 0;
            background: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: #FFE4E1;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .products-preview {
            padding: 4rem 0;
            background: #FFE4E1;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            color: #8B4B8B;
            margin-bottom: 3rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            height: 200px;
            background: linear-gradient(45deg, #D8BFD8, #FFB6C1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #8B4B8B;
            margin-bottom: 0.5rem;
        }

        .product-price {
            font-size: 1.5rem;
            color: #FF69B4;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        footer {
            background: #8B4B8B;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #FFB6C1;
        }

        .footer-section a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #FFB6C1;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                justify-content: center;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>مرحباً بكم في فيلا الحلويات</h1>
            <p>أجود أنواع الحلويات الشرقية والغربية بأفضل الأسعار</p>
            <a href="products.html" class="btn">تسوق الآن</a>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2 class="section-title">لماذا تختار فيلا الحلويات؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <h3>جودة عالية</h3>
                    <p>نستخدم أجود المكونات الطبيعية في صنع حلوياتنا</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚚</div>
                    <h3>توصيل سريع</h3>
                    <p>توصيل مجاني للطلبات أكثر من 50,000 دينار عراقي</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💝</div>
                    <h3>تغليف أنيق</h3>
                    <p>تغليف مميز يناسب جميع المناسبات والهدايا</p>
                </div>
            </div>
        </div>
    </section>

    <section class="products-preview">
        <div class="container">
            <h2 class="section-title">منتجاتنا المميزة</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">🧁</div>
                    <div class="product-info">
                        <h3 class="product-title">كب كيك الفانيليا</h3>
                        <p class="product-price">15,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">🍰</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة الشوكولاتة</h3>
                        <p class="product-price">45,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">🍪</div>
                    <div class="product-info">
                        <h3 class="product-title">كوكيز اللوز</h3>
                        <p class="product-price">25,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <a href="products.html" class="btn">عرض جميع المنتجات</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
