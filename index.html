<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيلا الحلويات - متجر الحلويات الأول</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="advanced-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-brown: #916045;
            --light-beige: #DEDIBA;
            --warm-gold: #D4A574;
            --cream: #F5F1E8;
            --dark-brown: #6B4423;
            --accent-gold: #B8956A;
            --text-dark: #2C1810;
            --text-light: #8B7355;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, var(--cream) 0%, var(--light-beige) 50%, #F8F5F0 100%);
            color: var(--text-dark);
            line-height: 1.7;
            font-weight: 400;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
            padding: 1.2rem 0;
            box-shadow: 0 8px 32px rgba(145, 96, 69, 0.15);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            font-size: 2.2rem;
            font-weight: 800;
            color: var(--cream);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            color: var(--warm-gold);
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 2.5rem;
            flex-wrap: wrap;
            align-items: center;
        }

        nav a {
            text-decoration: none;
            color: var(--cream);
            font-weight: 500;
            font-size: 1.05rem;
            position: relative;
            transition: all 0.3s ease;
            padding: 0.5rem 0;
        }

        nav a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--warm-gold);
            transition: width 0.3s ease;
        }

        nav a:hover {
            color: var(--warm-gold);
        }

        nav a:hover::after {
            width: 100%;
        }

        .cart-icon {
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .cart-icon:hover {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.4);
        }

        .hero {
            background: linear-gradient(135deg,
                rgba(145, 96, 69, 0.95) 0%,
                rgba(107, 68, 35, 0.9) 50%,
                rgba(145, 96, 69, 0.95) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="20" cy="20" r="2" fill="%23D4A574" opacity="0.1"/><circle cx="80" cy="60" r="1.5" fill="%23DEDIBA" opacity="0.15"/><circle cx="50" cy="90" r="1" fill="%23F5F1E8" opacity="0.1"/></pattern></defs><rect fill="url(%23grain)" width="1200" height="600"/></svg>');
            text-align: center;
            padding: 6rem 0;
            color: var(--cream);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(212, 165, 116, 0.2) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(222, 219, 186, 0.15) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
            text-shadow: 2px 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(135deg, var(--cream) 0%, var(--warm-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 3rem;
            text-shadow: 1px 2px 4px rgba(0,0,0,0.3);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 300;
            opacity: 0.95;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            color: white;
            padding: 1.2rem 3rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(212, 165, 116, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--primary-brown) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(212, 165, 116, 0.4);
        }

        .features {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--cream) 0%, white 100%);
            position: relative;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23D4A574" opacity="0.05"/></svg>');
            pointer-events: none;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
            margin-top: 3rem;
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: linear-gradient(135deg, white 0%, var(--light-beige) 100%);
            padding: 3rem 2rem;
            border-radius: 24px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 40px rgba(145, 96, 69, 0.1);
            border: 1px solid rgba(212, 165, 116, 0.2);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(145, 96, 69, 0.15);
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            filter: drop-shadow(0 4px 8px rgba(145, 96, 69, 0.2));
        }

        .feature-card h3 {
            color: var(--primary-brown);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: var(--text-light);
            line-height: 1.6;
            font-size: 1.05rem;
        }

        .products-preview {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--light-beige) 0%, var(--cream) 100%);
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-brown);
            margin-bottom: 4rem;
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            border-radius: 2px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 3rem;
            position: relative;
            z-index: 2;
        }

        .product-card {
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(145, 96, 69, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(212, 165, 116, 0.15);
            position: relative;
            group: hover;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(212, 165, 116, 0.05) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(145, 96, 69, 0.2);
        }

        .product-image {
            height: 240px;
            background: linear-gradient(135deg, var(--warm-gold) 0%, var(--accent-gold) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .product-info {
            padding: 2rem;
            position: relative;
        }

        .product-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-brown);
            margin-bottom: 0.8rem;
            line-height: 1.3;
        }

        .product-price {
            font-size: 1.8rem;
            color: var(--warm-gold);
            font-weight: 800;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .product-price::before {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--warm-gold);
            border-radius: 50%;
        }

        footer {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
            color: var(--cream);
            padding: 4rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%23D4A574" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="%23DEDIBA" opacity="0.08"/></svg>');
            pointer-events: none;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
            position: relative;
            z-index: 2;
        }

        .footer-section {
            position: relative;
        }

        .footer-section h3 {
            margin-bottom: 1.5rem;
            color: var(--warm-gold);
            font-size: 1.3rem;
            font-weight: 700;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: var(--warm-gold);
            border-radius: 1px;
        }

        .footer-section a {
            color: var(--cream);
            text-decoration: none;
            display: block;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
            padding: 0.3rem 0;
            position: relative;
            font-weight: 400;
        }

        .footer-section a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 1px;
            background: var(--warm-gold);
            transition: width 0.3s ease;
        }

        .footer-section a:hover {
            color: var(--warm-gold);
            padding-left: 1rem;
        }

        .footer-section a:hover::before {
            width: 12px;
        }

        .footer-section p {
            color: var(--cream);
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .container {
                padding: 0 20px;
            }

            .hero h1 {
                font-size: 3.5rem;
            }

            .features-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1.5rem;
                text-align: center;
            }

            nav ul {
                justify-content: center;
                gap: 1.5rem;
            }

            .hero {
                padding: 4rem 0;
            }

            .hero h1 {
                font-size: 2.8rem;
                line-height: 1.1;
            }

            .hero p {
                font-size: 1.1rem;
                margin-bottom: 2rem;
            }

            .btn {
                padding: 1rem 2rem;
                font-size: 1rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .features {
                padding: 4rem 0;
            }

            .products-preview {
                padding: 4rem 0;
            }

            .features-grid,
            .products-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .feature-card {
                padding: 2rem 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 16px;
            }

            .logo {
                font-size: 1.8rem;
            }

            .hero h1 {
                font-size: 2.2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }
        }

        /* Advanced Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .feature-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }

        .product-card {
            animation: slideInRight 0.6s ease forwards;
        }

        .product-card:nth-child(1) { animation-delay: 0.1s; }
        .product-card:nth-child(2) { animation-delay: 0.2s; }
        .product-card:nth-child(3) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">🍰 فيلا الحلويات</a>
                <nav>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                        <li><a href="account.html">حسابي</a></li>
                    </ul>
                </nav>
                <a href="cart.html" class="cart-icon">🛒 السلة (0)</a>
            </div>
        </div>
    </header>

    <section class="hero hero-advanced">
        <div class="container">
            <div class="hero-content">
                <h1 class="typewriter">مرحباً بكم في فيلا الحلويات</h1>
                <p class="scroll-reveal">أجود أنواع الحلويات الشرقية والغربية المصنوعة بحب وإتقان من أجود المكونات الطبيعية</p>
                <a href="products.html" class="btn btn-advanced btn-glow scroll-reveal">
                    <span>استكشف مجموعتنا الفاخرة</span>
                </a>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2 class="section-title scroll-reveal">لماذا تختار فيلا الحلويات؟</h2>
            <div class="features-grid">
                <div class="feature-card scroll-reveal">
                    <div class="feature-icon">🏆</div>
                    <h3>جودة عالية</h3>
                    <p>نستخدم أجود المكونات الطبيعية في صنع حلوياتنا</p>
                </div>
                <div class="feature-card scroll-reveal">
                    <div class="feature-icon">🚚</div>
                    <h3>توصيل سريع</h3>
                    <p>توصيل مجاني للطلبات أكثر من 50,000 دينار عراقي</p>
                </div>
                <div class="feature-card scroll-reveal">
                    <div class="feature-icon">💝</div>
                    <h3>تغليف أنيق</h3>
                    <p>تغليف مميز يناسب جميع المناسبات والهدايا</p>
                </div>
            </div>
        </div>
    </section>

    <section class="products-preview">
        <div class="container">
            <h2 class="section-title">منتجاتنا المميزة</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">🧁</div>
                    <div class="product-info">
                        <h3 class="product-title">كب كيك الفانيليا</h3>
                        <p class="product-price">15,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">🍰</div>
                    <div class="product-info">
                        <h3 class="product-title">تورتة الشوكولاتة</h3>
                        <p class="product-price">45,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">🍪</div>
                    <div class="product-info">
                        <h3 class="product-title">كوكيز اللوز</h3>
                        <p class="product-price">25,000 د.ع</p>
                        <a href="product-details.html" class="btn">عرض التفاصيل</a>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <a href="products.html" class="btn">عرض جميع المنتجات</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <a href="about.html">من نحن</a>
                    <a href="contact.html">اتصل بنا</a>
                    <a href="faq.html">الأسئلة الشائعة</a>
                </div>
                <div class="footer-section">
                    <h3>خدمات العملاء</h3>
                    <a href="shipping.html">سياسة الشحن</a>
                    <a href="payment.html">طرق الدفع</a>
                    <a href="terms.html">الشروط والأحكام</a>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📞 07XX XXX XXXX</p>
                    <p>📧 <EMAIL></p>
                    <p>📍 بغداد، العراق</p>
                </div>
            </div>
            <p>&copy; 2024 فيلا الحلويات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script src="advanced-scripts.js"></script>
    <script>
        // إضافة منتج إلى السلة
        function addToCart(productName, price, productId) {
            const product = {
                id: productId || Date.now(),
                name: productName,
                price: parseInt(price.replace(/[^\d]/g, '')),
                image: '🧁' // يمكن تخصيصها حسب المنتج
            };

            CartManager.addItem(product);
        }

        // تهيئة التأثيرات المتقدمة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة فئات التأثيرات للعناصر
            document.querySelectorAll('.product-card').forEach((card, index) => {
                card.classList.add('scroll-reveal', 'product-card-advanced');
                card.style.animationDelay = (index * 0.1) + 's';
            });

            document.querySelectorAll('.product-image').forEach(img => {
                img.classList.add('product-image-advanced');
            });

            // تحديث أزرار الإضافة للسلة
            document.querySelectorAll('.btn').forEach(btn => {
                if (btn.textContent.includes('إضافة')) {
                    btn.classList.add('btn-advanced');
                }
            });
        });
    </script>
</body>
</html>
